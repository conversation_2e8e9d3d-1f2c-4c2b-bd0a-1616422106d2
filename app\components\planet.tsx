export const PlanetLight = () => {
  return (
    <div className="absolute w-full top-0 left-0 hidden sm:block">
      <img
        className="w-52 sm:w-[716px] sm:h-[558px] absolute top-0 left-0 animate-orbit"
        style={{
          translate: "calc(500px - 350px) 110px",
        }}
        src="data:image/png;base64,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"
        alt=""
      />
    </div>
  );
};

export const PlanetAstronaut = () => {
  return (
    <div className="w-full sm:max-w-[800px] m-auto pt-[400px] sm:pt-[450px] lg:pt-[450px] md:px-8">
      {/* <img src="/images/top.png" className="w-[500px] hero-image" /> */}
      <video
        autoPlay
        loop
        muted
        src="https://a.slack-edge.com/0cedc3b/marketing/img/homepage/true-prospects/hero-revamp/animation/<EMAIL>"
      ></video>
    </div>
  );
};

export const Planet = ({ children }: { children?: React.ReactNode }) => {
  return (
    <div
      className="absolute top-0 left-[50%] w-full sm:w-[1000px] flex justify-center"
      style={{
        transform: "translate(-50%)",
      }}
    >
      <div className="absolute w-full top-0">
        <div className="absolute top-0 left-1/2 -translate-x-1/2 h-[calc(100vw/2)] sm:h-[1000px] w-full z-3 bg-background rounded-[50%] planet-circle">
          {children}
        </div>
      </div>
      <PlanetLight />
    </div>
  );
};
