import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { useState } from "react";

export const logos = [
  {
    name: "Microsoft",
    src: "images/brands/microsoft.svg",
  },
  {
    name: "Google",
    src: "images/brands/google.svg",
  },
  {
    name: "Stripe",
    src: "images/brands/stripe.svg",
  },
];

export const LogosSlider = () => {
  const breakpoints = {
    350: {
      slidesPerView: 5,
      spaceBetween: 0,
    },
    768: {
      slidesPerView: 8,
      spaceBetween: 10,
    },
    1024: {
      slidesPerView: 12,
      spaceBetween: 10,
    },
  };
  return (
    <Swiper modules={[Navigation]} breakpoints={breakpoints}>
      {logos.map((logo, index) => (
        <SwiperSlide key={index} className="relative cursor-grab group">
          <img
            src={logo.src}
            width={60}
            height={50}
            alt={logo.name}
            className="mx-auto"
          />
          <span className="absolute p-1 text-xs text-white transition-all duration-200 ease-out transform -translate-x-1/2 -translate-y-1/2 bg-blue-900 rounded-md opacity-0 top-full group-hover:opacity-100 group-hover:top-1/2 left-1/2">
            {logo.name}
          </span>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};
