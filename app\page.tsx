import CTAWilliam from "./components/william/CTAWilliam";
import <PERSON> from "./components/hero";
import SolutionWilliam from "./components/william/SolutionWilliam";
import Testimonials from "./components/william/Testimonials";
import FAQSection from "./components/FAQSection";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Inloop AI Agent for Viral Linkedin Content Creation",
  description:
    "<PERSON><PERSON><PERSON><PERSON>, AI Agent for LinkedIn Content. Create Viral Posts at Scale. AI Agent Automates Content Calendar powered by Trending Posts, Industry News, and Templates. Build Brand on Linkedin with an AI Agent.",
  keywords:
    "tryinloop, william ai agent, william ai colleague, sales team micro influencers, ai for sales teams",
  openGraph: {
    title: "Inloop AI Agent for Viral Linkedin Content Creation",
    description:
      "<PERSON><PERSON><PERSON><PERSON>, AI Agent for LinkedIn Content. Create Viral Posts at Scale. AI Agent Automates Content Calendar powered by Trending Posts, Industry News, and Templates. Build Brand on Linkedin with an AI Agent.",
    url: "https://tryinloop.com",
    images: [
      {
        url: "https://tryinloop.com/meta-images/home.png",
        width: 800,
        height: 400,
      },
    ],
  },
  alternates: {
    canonical: "https://tryinloop.com",
  },
};

export default function Home() {
  const heroHeadingLine1 = "Meet William";
  const heroHeadingLine2 = "AI Agent for Linkedin";
  const heroParagraph =
    "William, automates Linkedin content creation using Trending Topics, Industry News, and Viral Templates. Use him to build a Personal Brand or turn your team into Linkedin Micro Influencers.";

  return (
    <main className="relative mt-16">
      <Hero
        headingLine1={heroHeadingLine1}
        headingLine2={heroHeadingLine2}
        paragraph={heroParagraph}
      />
      <SolutionWilliam />
      <Testimonials />
      <FAQSection />
      <CTAWilliam />
    </main>
  );
}
