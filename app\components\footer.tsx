"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { CircleLogo } from "./circle-logo";
import TwitterIcon from "../icons/twitter";
import MailIcon from "../icons/email";

const Footer = () => {
  return (
    <footer className="relative z-50 mt-20">
      {/* Background decorative elements */}
      <div className="absolute -z-10 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-primary/[0.03] rounded-full blur-[130px] -translate-x-1/2" />
        <div className="absolute bottom-0 right-1/4 w-[500px] h-[500px] bg-secondary/[0.03] rounded-full blur-[130px] translate-x-1/2" />
      </div>

      <div className="relative w-full container px-0 md:px-4">
        <motion.div
          className="flex flex-col justify-between lg:flex-row"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          {/* Logo and Description Section */}
          <div className="md:w-6/12 lg:w-3/12 hidden sm:block">
            <div className="w-full mb-10">
              <Link href="/" className="mb-2 inline-block group">
                <div className="relative">
                  <div className="absolute -inset-2 bg-linear-to-r from-primary/20 via-secondary/20 to-primary/20 rounded-full blur-xs opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <CircleLogo size="small" />
                </div>
              </Link>
              <p className="text-sm mt-6 text-muted-foreground/80">
                Most Advanced AI Agent for Linkedin Content Automation
                Colleagues
              </p>

              {/* Social Icons */}
              <div className="flex items-center gap-4">
                <IconLink
                  href="https://twitter.com/tryinloop"
                  icon={TwitterIcon}
                  ariaLabel="Follow us on Twitter"
                />
                <IconLink
                  href="mailto:<EMAIL>"
                  icon={MailIcon}
                  ariaLabel="Send us an email"
                />
              </div>
            </div>
          </div>

          {/* Links Section */}
          <div className="flex flex-wrap justify-center text-center sm:justify-between sm:text-left lg:justify-end lg:w-8/12 gap-x-8 sm:gap-x-12">
            {/* Company Links */}
            <div className="w-1/2 sm:w-auto mb-8">
              <h4 className="mb-4 text-sm font-medium bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
                Company
              </h4>
              <ul className="space-y-3">
                <FooterLink href="/blog" text="Blog" />
                <FooterLink href="/about" text="About Us" />
              </ul>
            </div>

            {/* Tools Links */}
            <div className="w-1/2 sm:w-auto mb-8">
              <h4 className="mb-4 text-sm font-medium bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
                Tools
              </h4>
              <ul className="space-y-3">
                <FooterLink
                  href="/tools/linkedin-video-downloader"
                  text="LinkedIn Video Downloader"
                />
                <FooterLink
                  href="/tools/youtube-video-downloader"
                  text="YouTube Video Downloader"
                />
              </ul>
            </div>

            {/* Product Links */}
            <div className="w-1/2 sm:w-auto mb-8">
              <h4 className="mb-4 text-sm font-medium bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
                Product
              </h4>
              <ul className="space-y-3">
                <FooterLink href="/pricing" text="Pricing" />
                <FooterLink href="/discount-code" text="Discount Code" />
              </ul>
            </div>

            {/* Programs Links */}
            <div className="w-1/2 sm:w-auto mb-8">
              <h4 className="mb-4 text-sm font-medium bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
                Programs
              </h4>
              <ul className="space-y-3">
                <FooterLink href="/creator-program" text="Creator Program" />
                <FooterLink
                  href="/affiliate-program"
                  text="Affiliate Program"
                />
              </ul>
            </div>

            {/* Legal Links */}
            <div className="w-1/2 sm:w-auto mb-8">
              <h4 className="mb-4 text-sm font-medium bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
                Legal
              </h4>
              <ul className="space-y-3">
                <FooterLink href="/privacy-policy" text="Privacy Policy" />
                <FooterLink href="/terms-of-service" text="Terms of Service" />
              </ul>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Copyright Section */}
      <motion.div
        className="relative py-6"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute left-0 right-0 inset-0 bg-linear-to-r from-primary/[0.02] via-secondary/[0.02] to-primary/[0.02]" />
        <div className="absolute inset-0 backdrop-blur-3xl" />
        <div className="relative max-w-6xl mx-auto">
          <div className="w-full text-center">
            <span className="text-sm text-muted-foreground/70">
              © 2025{" "}
              <Link
                href="/"
                className="hover:text-primary/90 transition-colors duration-200"
              >
                Inloop
              </Link>
              . All Rights Reserved.
            </span>
          </div>
        </div>
      </motion.div>
    </footer>
  );
};

const IconLink = ({
  href,
  icon: Icon,
  ariaLabel,
}: {
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  ariaLabel: string;
}) => (
  <Link
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    className="group relative p-2 rounded-full bg-muted/30 hover:bg-muted/50 transition-all duration-200"
    aria-label={ariaLabel}
  >
    <div className="absolute -inset-1 bg-linear-to-r from-primary/20 to-secondary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    <Icon className="relative w-4 h-4 text-muted-foreground/70 group-hover:text-foreground transition-colors duration-200" />
  </Link>
);

const FooterLink = ({
  href,
  text,
  external = false,
}: {
  href: string;
  text: string;
  external?: boolean;
}) => (
  <li>
    <Link
      href={href}
      className="group relative inline-block text-sm text-muted-foreground/70 hover:text-muted-foreground transition-colors duration-200"
      {...(external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
    >
      <span className="relative">
        {text}
        <span className="absolute -bottom-0.5 left-0 w-0 h-[1px] bg-linear-to-r from-primary/40 to-secondary/40 group-hover:w-full transition-all duration-300" />
      </span>
    </Link>
  </li>
);

export default Footer;
