export const GridLines = () => {
  // Reduce the number of lines for better performance
  const verticalPositions = [1/6, 1/4, 1/3, 1/2, 2/3, 3/4, 5/6];
  const horizontalPositions = [1/6, 1/4, 1/3, 1/2, 2/3, 3/4, 5/6];
  const diagonalAngles = [-30, -15, 0, 15, 30];
  
  return (
    <div className="fixed inset-0 z-0 pointer-events-none w-screen h-screen overflow-hidden">
      {/* Vertical lines - reduced */}
      {verticalPositions.map((position, index) => (
        <div
          key={`line-v-${index}`}
          className="absolute top-0 bottom-0 w-[1px] bg-linear-to-b from-primary/3 via-primary/5 to-primary/3 transform-gpu opacity-80"
          style={{ left: `${position * 100}%` }}
        >
          <div className="absolute inset-0 blur-[1px] bg-linear-to-b from-primary/10 via-primary/15 to-primary/10 opacity-20" />
        </div>
      ))}

      {/* Horizontal lines - reduced */}
      {horizontalPositions.map((position, index) => (
        <div
          key={`line-h-${index}`}
          className="absolute left-0 right-0 h-[1px] bg-linear-to-r from-transparent via-primary/5 to-transparent transform-gpu opacity-80"
          style={{ top: `${position * 100}%` }}
        >
          <div className="absolute inset-0 blur-[1px] bg-linear-to-r from-transparent via-primary/15 to-transparent opacity-20" />
        </div>
      ))}

      {/* Diagonal lines - reduced */}
      {diagonalAngles.map((angle, index) => (
        <div
          key={`diagonal-${index}`}
          className="absolute inset-0 opacity-10 transform-gpu"
          style={{ transform: `rotate(${angle}deg) scale(1.2)` }}
        >
          <div className="absolute left-1/2 top-0 bottom-0 w-[1px] bg-linear-to-b from-primary/0 via-primary/5 to-primary/0">
            <div className="absolute inset-0 blur-[1px] bg-linear-to-b from-primary/5 via-primary/10 to-primary/5 opacity-20" />
          </div>
        </div>
      ))}
    </div>
  );
};

export default GridLines; 