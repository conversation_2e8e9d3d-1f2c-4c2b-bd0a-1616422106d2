import type { Metada<PERSON> } from "next";
import { GeistSans } from "geist/font/sans";
import "./globals.css";
import Header from "./components/header";
import Footer from "./components/footer";
import TrackingScripts from "./components/TrackingScripts";
import { ThemeProvider } from "@/components/theme-provider";
import { GoogleTagManager } from "@next/third-parties/google";

export const metadata: Metadata = {
  title:
    "Inloop | AI-Powered Platform for Sales Teams to Become Micro Influencers",
  description:
    "Build thought leadership with AI Colleague and close more deals quickly. Trusted by Top Founders.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const dev = process.env.NODE_ENV !== "production";
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <TrackingScripts />
        {!dev && (
          <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID ?? ""} />
        )}
      </head>
      <body className={`${GeistSans.className}`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <div className="px-4">
            <Header />
            {children}
          </div>
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  );
}
