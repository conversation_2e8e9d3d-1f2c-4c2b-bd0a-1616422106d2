import TextGradient from "@/components/ui/text-gradient";
import { Planet } from "../components/planet";
import Particles from "../components/particles";
import CallToAction from "../components/william/CTAWilliam";
import { cn } from "@/lib/utils";
import { Linkedin } from "lucide-react";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "About Inloop | Meet the Team Behind AI Sales Empowerment",
  description: "About Inloop",
  keywords:
    "about tryinloop team, creators of william ai agent, creators of william ai colleague, founders of tryinloop, founders of william ai agent",
  openGraph: {
    title: "About Inloop",
    description: "About Inloop",
    url: "https://tryinloop.com/about",
    images: [
      {
        url: "https://tryinloop.com/meta-images/about-us.png",
        width: 800,
        height: 400,
      },
    ],
  },
};

const LinkedInButton = ({
  url,
  className,
}: {
  url: string;
  className?: string;
}) => {
  return (
    <Link
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={cn(
        "w-12 h-12 bg-white/10 rounded-full flex items-center justify-center cursor-pointer hover:shadow-sm hover:shadow-gray-500",
        className || ""
      )}
    >
      <Linkedin className="text-gray-400" />
    </Link>
  );
};

const TeamCard = ({
  bg,
  name,
  description,
  title,
  imageUrl,
  imageClassName,
  linkedIn,
}: {
  bg: string;
  name: string;
  description: string;
  title: string;
  imageUrl: string;
  imageClassName?: string;
  linkedIn: string;
}) => {
  return (
    <div className="relative hover:shadow-lg group border border-border bg-background/90 backdrop-blur-xl p-6 sm:p-10 shadow-sm rounded-3xl sm:h-[500px] overflow-hidden">
      <div className="absolute left-0 top-0 w-full h-full opacity-60 transition-all duration-500 group-hover:opacity-100">
        <img
          src={bg}
          className="w-full h-full object-cover object-center rounded-3xl"
        />
      </div>

      <div className={"sm:absolute sm:-bottom-3 sm:right-0 mb-4 sm:mb-0"}>
        <img
          src={imageUrl}
          className={cn(imageClassName, "w-full sm:w-[240px] blend-image")}
        />
      </div>
      <div className="hidden sm:block whitespace-pre absolute left-20 top-[40%] opacity-5 transition-all duration-500 group-hover:-translate-x-44">
        <h1 className="stencil text-[180px] font-bold">{name}</h1>
      </div>
      <div className="relative">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="mb-1 text-2xl sm:text-3xl leading-tight font-normal">
              {name}
            </h1>
            <TextGradient txt={title} />
          </div>
          <LinkedInButton className="sm:hidden" url={linkedIn} />
        </div>
        <p className="text-base text-gray-400 sm:leading-relaxed md:leading-relaxed mt-2">
          {description}
        </p>
      </div>
      <div className="absolute bottom-10 left-10">
        <LinkedInButton className="hidden sm:flex" url={linkedIn} />
      </div>
    </div>
  );
};

export default function Home() {
  return (
    <main className="relative overflow-hidden">
      <div className="sm:block hidden absolute top-0 left-[40%] opacity-[0.15] transform scale-150 gradient-blob"></div>
      <div className="mt-32 relative">
        <Planet />
        <Particles size="lg" />
      </div>
      <div className="max-w-3xl m-auto mb-16 relative sm:text-center pt-12 sm:pt-20">
        <h1 className="mb-4 text-4xl sm:text-5xl leading-tight font-medium">
          Our
          <TextGradient txt=" Team" />
        </h1>
        <p className="text-base sm:text-lg font-normal text-gray-400 sm:leading-relaxed md:leading-relaxed">
          We are united by the mission to make video games more immersive & our
          passion to build products that are loved. Over the past 10 years,
          we&apos;ve dedicated ourselves to various projects that have generated
          millions in revenue and touched millions of lives around the globe.
        </p>
      </div>
      <div className="max-w-5xl m-auto relative grid grid-cols-1 sm:grid-cols-2 gap-10">
        {/* <TeamCard
          bg={"/gradient/card-gradient2.webp"}
          name="Neha Alreja"
          description="With Big love for coding and marketing, proud Founder at thesparkclub.in aiming on changing lives of kids."
          title="Engineering & Co-founder"
          imageUrl="/team/neha.png"
        /> */}
        <TeamCard
          bg={"/gradient/card-gradient3.webp"}
          name="Sahil"
          description="Former Pro DOTA Player turned entrepreneur. Built GamingGene platform for video gamers, developers & manufacturers to connect, share, promote, and distribute products. Spent over a decade working in different well-funded startups managing PNL and building large businesses from scratch. Used to build tower defense games as a hobby."
          title="Co-founder - CEO"
          imageUrl="/team/sahil.png"
          linkedIn="https://www.linkedin.com/in/isahilsingh/"
        />
        <TeamCard
          bg={"/gradient/card-gradient7.webp"}
          name="Surya"
          description="Former Pro AOE Player. An entrepreneur at heart! Former CTO at canumeet.com (alternate to calendly), co-founder at airlyft (community management platform). Built a scalable fraud prevention architecture at RiskIdent to manage risk for millions of concurrent financial transactions."
          title="Co-founder - CTO"
          imageUrl="/team/surya.png"
          imageClassName="sm:h-[240px]! sm:w-auto!"
          linkedIn="https://www.linkedin.com/in/suryaps/"
        />
        {/* <TeamCard
          bg={"/gradient/card-gradient5.png"}
          name="Shashikant"
          description="Youngest hustler in the team. Won multiple coding competitions, eager to learn and grow."
          title="Engineering"
          imageUrl="/team/shashi.png"
          imageClassName="sm:h-[240px]! sm:w-auto!"
          linkedIn="https://www.linkedin.com/in/shashikant-001/"
        />
        <TeamCard
          bg={"/gradient/card-gradient3.webp"}
          name="Aslam"
          description="Relentless problem solver. Specialises in front end development but never shy away from any challenge. Owner of multiple open source projects."
          title="Engineering"
          imageUrl="/team/aslam.png"
          imageClassName="sm:h-[260px]! sm:w-auto!"
          linkedIn="https://www.linkedin.com/in/aslam-shah-79a8a016/"
        /> */}

        {/* <TeamCard
          bg={"/gradient/card-gradient6.png"}
          name="Rachit Magon"
          description="ex-CTO at FakirInformatic GmBh. Entrepreneur with huge love for problem solving. Founder at smooper.com and smoopit.com and working together to enhance prediction system"
          title="Advisor"
          imageUrl="/team/rachit.png"
          imageClassName="sm:h-[260px]! sm:w-auto!"
        /> */}
        {/* <TeamCard
          bg={"/gradient/card-gradient4.webp"}
          name="CBS"
          description="Fun-loving product enthusiast with deep expertise in product management and customer success. Former COO at Canumeet.com and founder at airlyft.one"
          title="Advisor"
          imageUrl="/team/chandra.png"
          imageClassName="sm:h-[260px]! sm:w-auto!"
          linkedIn="https://www.linkedin.com/in/chandra-bhushan-singh-381615a0/"
        /> */}
        {/* <TeamCard
          bg={"/gradient/card-gradient2.webp"}
          name="Muiz Nadeem"
          description="Calm and composed engineer specializing in breaking huge problems to smaller ones and then solving it."
          title="Advisor"
          imageUrl="/team/muiz.png"
          imageClassName="sm:h-[280px]! sm:w-auto!"
        /> */}
      </div>
      <CallToAction />
    </main>
  );
}
