"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { CircleLogo } from "./circle-logo";
import { ThemeToggle } from "@/components/theme-toggle";
import Link from "next/link";
import { useGtmTrack } from "@/lib/tracking";

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { loginTrack, bookDemoTrack } = useGtmTrack();

  useEffect(() => {
    // Set initial scroll state immediately
    setScrolled(window.scrollY > 20);
    // Delay mounted state to avoid Safari flicker
    requestAnimationFrame(() => {
      setMounted(true);
    });

    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  if (!mounted) return null;

  return (
    <header
      className={`w-full fixed top-0 left-0 z-40 transition-[padding] duration-300 ${
        scrolled ? "py-3" : "py-5"
      }`}
    >
      <nav className="m-auto container w-full flex justify-between items-center px-4">
        <div
          className="relative py-2 px-4 rounded-[26px] border border-border/40 
          flex items-center gap-x-4 shadow-sm overflow-hidden group"
        >
          {/* Backdrop blur in a separate layer */}
          <div className="absolute inset-0 bg-background/70 backdrop-blur-md" />
          <div className="absolute inset-0 bg-linear-to-r from-primary/10 to-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <Link href="/" className="relative z-10 flex items-center gap-2">
            <CircleLogo size="small" />
            <span className="font-medium text-foreground/90 hidden sm:block">
              inloop
            </span>
          </Link>
        </div>

        <div
          className="relative py-3 px-5 rounded-full border border-border/40
          flex justify-end items-center gap-x-5 shadow-sm"
        >
          {/* Backdrop blur in a separate layer */}
          <div className="absolute inset-0 bg-background/70 backdrop-blur-md rounded-full" />
          <div className="absolute inset-0 bg-linear-to-r from-primary/5 via-accent/5 to-secondary/5 rounded-full" />
          <div className="relative z-10">
            <ThemeToggle />
          </div>
          <a
            href="https://william.tryinloop.com/"
            target="_blank"
            className="relative z-10 text-foreground/90 font-medium hover:text-foreground transition-colors duration-200"
            onClick={loginTrack}
          >
            Login
          </a>
          <motion.a
            href="https://calendly.com/sahil-tryinloop/inloop-demo"
            className="relative z-10 text-sm font-medium items-center justify-center flex transition-all duration-300
            focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
            disabled:opacity-50 disabled:pointer-events-none ring-offset-background py-2.5 px-6
            text-primary-foreground bg-primary hover:bg-primary/90
            rounded-full shadow-sm hover:shadow-md"
            target="_blank"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={bookDemoTrack}
          >
            Book a Demo
          </motion.a>
        </div>
      </nav>
    </header>
  );
};

export default Header;
