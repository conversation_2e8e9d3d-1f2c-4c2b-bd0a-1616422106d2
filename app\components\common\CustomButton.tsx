import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

const CustomButton = ({
  href,
  children,
  className,
  disabled,
  onClick,
  target,
}: {
  href?: string;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  target?: string;
}) => {
  const buttonClasses = cn(
    buttonVariants({ size: "lg" }),
    "rounded-full h-10 px-6 font-semibold",
    className,
    disabled && "opacity-50 cursor-not-allowed"
  );

  if (disabled) {
    return (
      <button className={buttonClasses} onClick={onClick} disabled>
        {children}
      </button>
    );
  }

  return (
    <a href={href} className={buttonClasses} onClick={onClick} target={target}>
      {children}
    </a>
  );
};

export default CustomButton;
