import { AvatarCircles } from "@/components/magicui/avatar-circles";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, CheckCircle, Star, StarHalfIcon } from "lucide-react";
import { memo } from "react";
import { fadeInAnimation } from "../common/HeroDescription";
import { useGtmTrack } from "@/lib/tracking";
import TextGradient from "@/components/ui/text-gradient";
import { BorderBeam } from "@/components/magicui/border-beam";

interface HeroContentProps {
  headingY: any;
  headingLine1?: string;
  headingLine2?: string;
  paragraph?: string;
  buttonText?: string;
  hyphen?: boolean;
}

// Memoized star rating component
const StarRating = memo(() => (
  <div className="flex text-yellow-500">
    {[1, 2, 3, 4].map((i) => (
      <Star key={i} className="w-4 h-4 fill-current" />
    ))}
    <StarHalfIcon className="w-4 h-4 fill-current" />
  </div>
));

StarRating.displayName = "StarRating";

export const HeroContent = ({
  headingY,
  headingLine1,
  headingLine2,
  paragraph,
  buttonText,
}: HeroContentProps) => {
  const { getStartedFreeContentTrack } = useGtmTrack();
  // Placeholder avatar data
  const avatarUrls = [
    {
      imageUrl: "https://avatars.githubusercontent.com/u/213455?v=4",
      profileUrl: "#",
    },
    {
      imageUrl: "https://avatars.githubusercontent.com/u/876449?v=4",
      profileUrl: "#",
    },
    {
      imageUrl: "https://avatars.githubusercontent.com/u/1500684?v=4",
      profileUrl: "#",
    },
  ];

  return (
    <motion.div
      className="container px-4 sm:px-8 text-center z-30 mb-12 mt-28 will-change-transform"
      style={{ y: headingY }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <h1 className="mb-6 text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
        <span className="bg-clip-text text-transparent bg-linear-to-r from-foreground/95 to-foreground/80 mb-2">
          {headingLine1 || "Turn your Sales Team into"}{" "}
        </span>
        <TextGradient txt={headingLine2 || "Micro Sales Influencers"} />
      </h1>

      <motion.p
        className="m-auto text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed sm:max-w-4xl"
        {...fadeInAnimation}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        {paragraph ||
          "Build thought leadership with AI Colleague and close more deals quickly. Trusted by Top Founders."}
      </motion.p>

      <motion.div
        className="mt-6 flex flex-col items-center justify-center gap-y-5"
        {...fadeInAnimation}
        transition={{ delay: 0.3, duration: 0.4 }}
      >
        {/* Button with rotating border */}
        <div className="relative">
          <button
            className="group relative flex items-center gap-2 rounded-full border border-primary/50 bg-primary/5 px-8 py-3 font-medium text-primary shadow-sm backdrop-blur-sm transition-all duration-300 hover:bg-primary/10 hover:shadow-primary/20 active:translate-y-0.5 cursor-pointer"
            onClick={() => {
              getStartedFreeContentTrack();
              window.open("https://william.tryinloop.com", "_blank");
            }}
          >
            <span>{buttonText || "Get 1 Week Content for Free"}</span>
            <ArrowRight className="h-4 w-4 opacity-70 transition-transform duration-300 group-hover:translate-x-1" />
            {/* Border Beam Effect */}
            <BorderBeam
              size={40}
              duration={3.5}
              colorFrom="#9c40ff"
              colorTo="#ffaa40"
            />

            {/* Background subtle glow */}
            <div className="absolute inset-0 -z-10 rounded-full bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 opacity-0 blur-xl transition-opacity duration-500 group-hover:opacity-100"></div>
          </button>
        </div>

        {/* Reaffirmations */}
        <div className="flex items-center justify-center gap-x-6 text-sm text-muted-foreground flex-wrap">
          <span className="flex items-center gap-x-1 whitespace-nowrap">
            <CheckCircle className="w-4 h-4 text-primary" />
            No Credit Card Required
          </span>
          <span className="flex items-center gap-x-1 whitespace-nowrap">
            <CheckCircle className="w-4 h-4 text-primary" />
            Free Forever Plan
          </span>
        </div>
        <div className="flex items-center justify-center gap-x-2 flex-wrap">
          <AvatarCircles numPeople={97} avatarUrls={avatarUrls} />
          <span className="text-sm text-muted-foreground">
            Loved by Founders & Sales Leaders
          </span>
          <StarRating />
        </div>
      </motion.div>
    </motion.div>
  );
};

export default memo(HeroContent);
