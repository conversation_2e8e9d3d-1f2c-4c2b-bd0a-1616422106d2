"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useState } from "react";

const steps = [
  {
    img: "/images/how-it-works-1.png",
    title: "Smart Sources",
    description:
      "Pulls from articles you read to create content for every channel automatically.",
  },
  {
    img: "/images/how-it-works-1.png",
    title: "Auto Podcast Clips",
    description:
      "Converts your posts into engaging audio clips with your voice for social sharing.",
  },
  {
    img: "/images/how-it-works-1.png",
    title: "Integrated Calendar",
    description:
      "Visual content planner to schedule, manage, and optimize publishing cadence weekly.",
  },
];

const HowItWorks = () => {
  const [active, setActive] = useState(0);

  return (
    <motion.div
      className="relative mt-14 p-8 rounded-[32px] shadow-xs overflow-hidden bg-linear-to-b from-background/80 to-background/40 backdrop-blur-xl border border-primary/10"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      {/* Background decorative elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-primary/5 rounded-full blur-[120px] -translate-x-1/2" />
        <div className="absolute bottom-0 right-1/4 w-[500px] h-[500px] bg-secondary/5 rounded-full blur-[120px] translate-x-1/2" />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <div className="relative inline-block">
          <h2 className="mb-4 text-4xl sm:text-5xl leading-tight font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground via-foreground/90 to-foreground/80">
            How it works?
          </h2>
        </div>
        <motion.p
          className="text-base sm:text-lg sm:leading-relaxed md:text-xl md:leading-relaxed text-muted-foreground/80"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Turn AE/SDRs to Micro Sales Influencers
        </motion.p>
      </motion.div>

      <div className="hidden lg:block relative mt-12">
        <motion.div
          className="rounded-[24px] overflow-hidden aspect-video relative"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          {/* Border gradient */}
          <div className="absolute -inset-[1px] bg-linear-to-b from-primary/20 via-secondary/10 to-primary/20 rounded-[24px]" />

          {/* Image container */}
          <div className="absolute inset-[1px] rounded-[23px] overflow-hidden">
            <motion.img
              src={steps[active].img}
              alt={steps[active].title}
              className="w-full h-full object-cover"
              initial={{ scale: 1.1, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              key={active}
              transition={{ duration: 0.6 }}
            />
            <div className="absolute inset-0 bg-linear-to-t from-background/40 via-background/20 to-transparent" />
          </div>
        </motion.div>
      </div>

      <div className="hidden lg:grid mt-8 grid-cols-3 gap-6">
        {steps.map((s, idx) => (
          <StepsCard
            key={idx}
            heading={s.title}
            description={s.description}
            active={active === idx}
            action={() => setActive(idx)}
          />
        ))}
      </div>

      <div className="grid lg:hidden mt-8 grid-cols-1 gap-8">
        {steps.map((s, idx) => (
          <motion.div
            key={idx}
            className={cn(
              "relative p-6 rounded-2xl bg-linear-to-br from-background/40 to-background/20 backdrop-blur-xs border border-primary/10",
              steps.length - 1 === idx ? "" : ""
            )}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: idx * 0.2 }}
          >
            <h3 className="text-lg sm:text-2xl xl:text-3xl font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
              {s.title}
            </h3>
            <p className="text-base mt-2 text-muted-foreground">
              {s.description}
            </p>
            <motion.div
              className="rounded-2xl overflow-hidden aspect-video h-auto w-full mt-6"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="relative w-full h-full">
                <div className="absolute -inset-[1px] bg-linear-to-b from-primary/20 via-secondary/10 to-primary/20 rounded-2xl" />
                <div className="absolute inset-[1px] rounded-2xl overflow-hidden">
                  <img
                    src={s.img}
                    alt={s.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-linear-to-t from-background/40 via-background/20 to-transparent" />
                </div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

const StepsCard = (props: {
  heading: string;
  description: string;
  active: boolean;
  action: () => void;
}) => {
  return (
    <motion.div
      className={cn(
        "relative p-6 rounded-2xl transition-all duration-500",
        props.active
          ? "bg-linear-to-br from-primary/10 via-secondary/5 to-background/5 backdrop-blur-xs border-primary/20"
          : "hover:bg-linear-to-br hover:from-primary/5 hover:via-secondary/5 hover:to-background/5",
        "border border-primary/10 cursor-pointer group"
      )}
      onClick={props.action}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        initial={false}
        animate={{
          opacity: props.active ? 1 : 0.7,
          y: props.active ? 0 : 5,
        }}
        transition={{ duration: 0.3 }}
      >
        <h3 className="text-lg sm:text-xl xl:text-2xl font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
          {props.heading}
        </h3>
        <p className="text-base mt-2 text-muted-foreground group-hover:text-muted-foreground/80 transition-colors">
          {props.description}
        </p>
      </motion.div>

      <div
        className={cn(
          "absolute bottom-0 left-0 right-0 h-[2px] bg-linear-to-r from-transparent via-primary/50 to-transparent transition-opacity duration-300",
          props.active ? "opacity-100" : "opacity-0"
        )}
      />
    </motion.div>
  );
};

export default HowItWorks;
