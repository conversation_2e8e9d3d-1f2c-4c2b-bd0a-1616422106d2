import Link from "next/link";
import HeroDescription from "../components/common/HeroDescription";
import AnimatedButton from "../components/common/AnimatedButton";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Inloop Creator Program | Exclusive Discounts for Your Community",
  description: "Give 25% Saving to your Super Followers.",
  keywords:
    "tryinloop creator program, become a creator tryinloop, get sponsered by tryinloop",
  openGraph: {
    title: "Inloop Creator Program",
    description: "Give 25% Saving to your Super Followers.",
    url: "https://tryinloop.com/creator-program",
    images: [
      {
        url: "https://tryinloop.com/meta-images/creator-program.png",
        width: 800,
        height: 400,
      },
    ],
  },
};

export default function CreatorProgram() {
  return (
    <div className="py-10 mt-26 relative">
      <div className="max-w-5xl mx-auto">
        <HeroDescription
          headingLine1="Inloop"
          headingLine2="Creator Program"
          subHeading="Give 25% Saving to your Super Followers."
          paragraph={
            <>
              <p>
                The <b>InLoop Creator Program</b> is a simple, free way to give
                back more to your community. You can request your Creator Code
                by filling{" "}
                <Link
                  style={{ textDecoration: "underline" }}
                  href="https://docs.google.com/forms/d/e/1FAIpQLSfcoq_uronqamaOWF2Onpb-7Ofpi9iMlskD2e5k7eY8zfeXDA/viewform?usp=dialog"
                >
                  here
                </Link>
                . Your application will be reviewed and approved in 3 working
                days and the code will be shared to you via your email.
              </p>
              <AnimatedButton
                size="md"
                className="mt-8 justify-center w-72 sm:mx-auto"
                href="https://docs.google.com/forms/d/e/1FAIpQLSfcoq_uronqamaOWF2Onpb-7Ofpi9iMlskD2e5k7eY8zfeXDA/viewform?usp=dialog"
                showArrow={true}
              >
                Apply Here
              </AnimatedButton>
              <p className="mt-4">
                Once you get your code, you can share it with your Super
                Followers. The code can be directly applied at the payment.
              </p>
            </>
          }
        />

        <div className="space-y-12">
          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              Questions
            </h2>
            <div className="space-y-4 text-muted-foreground">
              <p className="mb-4 text-muted-foreground/90 leading-relaxed">
                Drop in a note to{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>

          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              Program Details
            </h2>
            <div className="space-y-4 text-muted-foreground">
              <p className="mb-4 text-muted-foreground/90 leading-relaxed">
                The program will be running till 1st October 2025.
              </p>
            </div>
          </div>

          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              Code Redemption Process
            </h2>
            <ol className="list-decimal pl-6 mb-6 space-y-3 text-muted-foreground/90">
              <li className="leading-relaxed">
                Sign up for Inloop at{" "}
                <a
                  href="https://tryinloop.com"
                  className="text-blue-600 hover:underline"
                >
                  tryinloop.com
                </a>
              </li>
              <li className="leading-relaxed">
                Go Credits &gt; Plans &gt; Choose Plan &gt; Payment Page
              </li>
              <li className="leading-relaxed">Apply Code</li>
              <li className="leading-relaxed">Pay</li>
            </ol>
          </div>

          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              Terms and Conditions
            </h2>
            <ul className="list-disc pl-6 mb-6 space-y-3 text-muted-foreground/90">
              <li className="leading-relaxed">
                Coupons are valid for a limited time only. TryInloop reserves
                the right to modify or cancel coupons at any time.
              </li>
              <li className="leading-relaxed">
                The coupon offer will not be valid until it is applied to the
                qualifying product.
              </li>
              <li className="leading-relaxed">
                The promotion is limited to one coupon per customer.
              </li>
              <li className="leading-relaxed">
                Promotion may not be combined with other promotions.
              </li>
              <li className="leading-relaxed">Void where prohibited.</li>
              <li className="leading-relaxed">
                TryInloop has no obligation for payment of any tax in
                conjunction with the distribution or use of any coupon.
              </li>
              <li className="leading-relaxed">
                Consumer is required to pay any applicable sales tax related to
                the use of the coupon.
              </li>
              <li className="leading-relaxed">
                Coupons are void if restricted or prohibited by law.
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
