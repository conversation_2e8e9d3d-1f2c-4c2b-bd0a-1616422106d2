"use server";

import axios from "axios";
import {
  detectPlatform,
  processVideoResponse,
  processAudioResponse,
  PLATFORMS,
} from "@/lib/platform-config";

interface VideoDownloadResult {
  success: boolean;
  data?: {
    videoOptions: any[];
    audioOptions?: any[];
    title?: string;
    thumbnail?: string;
  };
  error?: string;
}

export async function downloadVideoAction(
  url: string
): Promise<VideoDownloadResult> {
  try {
    if (!url) {
      return {
        success: false,
        error: "Please enter a video URL",
      };
    }

    const currentPlatform = detectPlatform(url);
    if (!currentPlatform) {
      return {
        success: false,
        error: "Please enter a valid video URL",
      };
    }

    const config = PLATFORMS[currentPlatform];
    if (config.status === "coming-soon") {
      return {
        success: false,
        error: `${config.name} support is coming soon`,
      };
    }

    const webhookUrl = new URL(
      "https://n8n.tryinloop.com/webhook/video-download"
    );
    const videoId = config.extractVideoId(url);

    webhookUrl.searchParams.set("platformType", currentPlatform);
    webhookUrl.searchParams.set("videoId", videoId);

    const authHeaders: Record<string, string> = {};

    if (
      process.env.WEBHOOK_AUTH_USERNAME &&
      process.env.WEBHOOK_AUTH_PASSWORD
    ) {
      const credentials = Buffer.from(
        `${process.env.WEBHOOK_AUTH_USERNAME}:${process.env.WEBHOOK_AUTH_PASSWORD}`
      ).toString("base64");
      authHeaders.Authorization = `Basic ${credentials}`;
    }

    const { data } = await axios.get(webhookUrl.toString(), {
      headers: {
        "User-Agent": "Inloop-VideoDownloader/1.0",
        Accept: "application/json",
        ...authHeaders,
      },
    });

    const videoOptions = processVideoResponse(data, currentPlatform);
    let audioOptions: any[] = [];

    if (currentPlatform === "Youtube") {
      audioOptions = processAudioResponse(data, currentPlatform);
    }

    return {
      success: true,
      data: {
        videoOptions,
        audioOptions,
        title: data.title || `${config.name} Video`,
        thumbnail: data.thumbnail,
      },
    };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 401) {
        return {
          success: false,
          error: "Authentication failed. Please check your credentials.",
        };
      }
      if (error.response?.status === 403) {
        return {
          success: false,
          error: "Access forbidden. Please check your permissions.",
        };
      }
      if (error.code === "ECONNABORTED") {
        return {
          success: false,
          error: "Request timeout. Please try again.",
        };
      }
    }

    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while getting the video",
    };
  }
}
