import { Metadata } from "next";
import YoutubeVideoDownloaderPage from "./components/YoutubeVideoDownloaderPage";

export const metadata: Metadata = {
  title: "YouTube Video Downloader | Download YouTube Videos - Inloop",
  description:
    "Download YouTube videos in multiple qualities with our free tool. Save YouTube videos to your device for offline viewing or content creation.",
  keywords: [
    "YouTube video downloader",
    "download YouTube videos",
    "YouTube downloader",
    "save YouTube videos",
    "video downloader tool",
  ],
  openGraph: {
    title: "YouTube Video Downloader | Download YouTube Videos - Inloop",
    description:
      "Download YouTube videos in multiple qualities with our free tool. Save YouTube videos to your device for offline viewing or content creation.",
    url: "https://tryinloop.com/tools/youtube-video-downloader",
    siteName: "Inloop",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "YouTube Video Downloader | Download YouTube Videos - Inloop",
    description:
      "Download YouTube videos in multiple qualities with our free tool. Save YouTube videos to your device for offline viewing or content creation.",
  },
  alternates: {
    canonical: "https://tryinloop.com/tools/youtube-video-downloader",
  },
};

export default function Page() {
  return <YoutubeVideoDownloaderPage />;
}
