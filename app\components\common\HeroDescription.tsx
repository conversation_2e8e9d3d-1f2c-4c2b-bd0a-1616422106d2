"use client";

import TextGradient from "@/components/ui/text-gradient";
import { motion } from "framer-motion";
import BlobAnimation from "./BlobAnimation";
import { ReactNode } from "react";

// Optimize animations by reducing their complexity
export const fadeInAnimation = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.4 },
};

export default function HeroDescription({
  headingLine1,
  headingLine2,
  paragraph,
  hyphen = false,
  subHeading,
}: {
  headingLine1?: string;
  headingLine2?: string;
  paragraph?: ReactNode;
  hyphen?: boolean;
  subHeading?: string;
}) {
  return (
    <div className="max-w-7xl m-auto mb-16 relative sm:text-center py-10">
      <BlobAnimation />
      <h1 className="mb-6 text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
        <span className="bg-clip-text text-transparent bg-linear-to-r from-foreground/95 to-foreground/80 mb-2">
          {headingLine1 || "Turn your Sales Team into"} {hyphen && " - "}
        </span>
        <TextGradient txt={headingLine2 || "Micro Sales Influencers"} />
      </h1>

      {subHeading && (
        <h2 className="mb-6 text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight">
          <span className="bg-clip-text text-transparent bg-linear-to-r from-foreground/95 to-foreground/80 mb-2">
            {subHeading}
          </span>
        </h2>
      )}

      <motion.div
        className="m-auto text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed sm:max-w-3xl"
        {...fadeInAnimation}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        {paragraph ||
          "Build thought leadership with AI Colleague and close more deals quickly. Trusted by Top Founders."}
      </motion.div>
    </div>
  );
}
