import { getComparisonBySlug, getComparisons } from "@/content/lib";
import { ArrowLeft } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";
import { ParsedUrlQuery } from "querystring";
import { FC, use } from "react";

interface Params extends ParsedUrlQuery {
  slug: string;
}

type Props = {
  params: Params;
};

const getInitialComparison = async (slug: string) => {
  const comparison = getComparisonBySlug(slug);
  return comparison;
};

const ComparisonDetail: FC<Props> = ({ params }) => {
  const comparison = use(getInitialComparison(params.slug));

  return (
    <section className="relative mt-28 overflow-hidden">
      <div className="relative z-10 mx-auto">
        <div className="mx-auto mt-10 rounded max-w-prose lg:text-lg gap-y-10 gap-x-6">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">{comparison.title}</h1>
            <p className="text-lg text-muted-foreground">{comparison.description}</p>
            {comparison.competitor && (
              <p className="text-sm text-muted-foreground mt-2">
                Comparing with: <span className="font-semibold">{comparison.competitor}</span>
              </p>
            )}
          </div>

          {/* Content */}
          <article
            className="mx-auto mt-8 prose lg:prose-lg dark:prose-invert 
            prose-headings:text-foreground prose-headings:font-bold 
            prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl
            prose-p:text-muted-foreground prose-p:leading-relaxed
            prose-a:text-primary prose-a:no-underline hover:prose-a:underline
            prose-strong:text-foreground prose-strong:font-semibold
            prose-code:text-primary prose-code:bg-muted prose-code:rounded prose-code:px-1 prose-code:py-0.5
            prose-pre:bg-muted prose-pre:text-muted-foreground prose-pre:border prose-pre:border-border
            prose-blockquote:text-muted-foreground prose-blockquote:border-l-primary
            prose-img:rounded-lg prose-img:mx-auto prose-img:w-full
            prose-ul:text-muted-foreground prose-ol:text-muted-foreground
            prose-li:marker:text-primary
            prose-table:w-full prose-table:border-collapse
            prose-th:border prose-th:border-border prose-th:bg-muted prose-th:p-2 prose-th:text-left
            prose-td:border prose-td:border-border prose-td:p-2"
          >
            <div dangerouslySetInnerHTML={{ __html: comparison.content }} />
          </article>

          {/* Back button */}
          <div className="flex justify-start mt-8">
            <Link
              href="/comparison"
              className="flex items-center justify-center px-4 py-2 text-base font-medium text-primary transition duration-300 ease-in-out bg-white bg-opacity-0 border-2 border-primary rounded-full hover:bg-primary hover:text-primary-foreground hover:shadow-lg"
            >
              <ArrowLeft size={24} className="mr-2" /> Back to Comparisons
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const comparison = await getComparison(params.slug);

  return {
    title: comparison.title,
    description: comparison.description,
    openGraph: {
      title: comparison.title,
      description: comparison.description,
      url: `https://tryinloop.com/comparison/simple/${comparison.slug}`,
      images: [
        {
          url: `https://tryinloop.com/meta-images/comparison-${comparison.slug}.png`,
          width: 800,
          height: 400,
        },
      ],
    },
  };
}

async function getComparison(slug: string) {
  const comparison = getComparisonBySlug(slug);
  return comparison;
}

export function generateStaticParams() {
  const comparisons = getComparisons();

  return comparisons.map((comparison) => ({
    slug: comparison.slug,
  }));
}

export default ComparisonDetail;
