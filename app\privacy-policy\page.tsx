import BackgroundGrid from "../components/hero/BackgroundGrid";
import PrivacyPolicyBody from "../components/privacy-policy-body";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Inloop Privacy Policy | How We Protect Your Data & Information",
  description:
    "Your privacy is important to us. This policy outlines how we collect, use, and protect your data.",
  keywords: "tryinloop privacy policy",
  openGraph: {
    title: "Privacy Policy",
    description:
      "Your privacy is important to us. This policy outlines how we collect, use, and protect your data.",
    url: "https://tryinloop.com/privacy-policy",
    images: [
      {
        url: "https://tryinloop.com/meta-images/privacy-policy.png",
        width: 800,
        height: 400,
      },
    ],
  },
};
export default function PrivacyPolicy() {
  return (
    <div className="mt-32">
      <BackgroundGrid />
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none" />
      <PrivacyPolicyBody />
    </div>
  );
}
