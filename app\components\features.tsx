import TextGradient from "@/components/ui/text-gradient";
import FeatureCard from "./feature-card";

export default function Features() {
  return (
    <div className="max-w-5xl m-auto relative">
      <div className="sm:text-center sm:mb-16 ">
        <h1 className="text-4xl sm:text-5xl leading-tight font-medium">
          Inloop brings <TextGradient txt=" more creativity and efficiency" />{" "}
          to game developers
        </h1>
      </div>
      <div className="space-y-10 relative">
        <FeatureCard
          bg={"/gradient/card-gradient2.webp"}
          title={"Inloop Lab"}
          imageUrl="/images/lab.png"
          description={
            "Bring in-game characters to life with Inloop Lab. Give characters, personality with a background story, behavioral traits, voice and more. Add minor improvisation to full improvisation for emergent gameplay and see characters engage players in deeply personalized interactions and evolve with them."
          }
        />
        <FeatureCard
          bg={"/gradient/card-gradient1.webp"}
          title="Dynamic Actions"
          imageUrl="/images/actions.png"
          description={
            "Add actions including mission, rewards and goals to in-game characters and trigger them with ingame events. Swiftly update characters to steer players towards unexplored game elements."
          }
        />
        {/* <FeatureCard
          bg={"/gradient/card-gradient7.webp"}
          title="NPC Asset Marketplace"
          imageUrl="/alien/alien3.png"
          description={[
            "Find new and rich NPC assets in the marketplace and purchase or lease them to make the game more interesting and reduce development time.",
            "Smart contracts manage transactions and leases for transparency and scalability.",
          ]}
        /> */}
        <FeatureCard
          bg={"/gradient/card-gradient7.webp"}
          title="Analytics Dashboard"
          imageUrl="/images/dashboard.png"
          description={
            "Track and analyse performance of the agents with an analytics dashboard. Fine tune storyline, content, actions and more to keep players wanting for more."
          }
        />
        <FeatureCard
          bg={"/gradient/card-gradient3.webp"}
          title="Quick Deployment"
          imageUrl="/images/deployment.png"
          imageClassName="sm:w-[470px]!"
          description={
            "Rapidly integrate Inloop in-game characters using specialized Unity, Unreal, and JS SDKs. Inloop agents offer full flexibility, fitting effortlessly into your workflow at any stage. Easily customize and extend these versatile tools to bring your design concepts to life."
          }
        />
      </div>
    </div>
  );
}
