@import "tailwindcss";

@plugin 'tailwindcss-animate';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-orbit: lights-orbit 8s linear infinite;
  --animate-orbit-reverse: lights-orbit 8s linear infinite reverse;

  --timing-function-custom: cubic-bezier(0.6, 0.6, 0, 1);

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes lights-orbit {
    from {
      transform: rotate(0) translate(-200px, 0) rotate(0);
    }
    to {
      transform: rotate(360deg) translate(-200px, 0.03px) rotate(-360deg);
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  .planet-circle:before {
    background: linear-gradient(
      to top,
      transparent 70%,
      rgba(255, 255, 255, 0.05) 100%
    );
    border-radius: inherit;
    content: "";
    inset: 0;
    -webkit-mask: linear-gradient(var(--particle-color-3) 0 0) content-box,
      linear-gradient(var(--particle-color-3) 0 0);
    mask: linear-gradient(var(--particle-color-3) 0 0) content-box,
      linear-gradient(var(--particle-color-3) 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    padding: 1px;
    pointer-events: none;
    position: absolute;
  }

  .planet-circle:after {
    content: "";
    border-radius: inherit;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;

    background: linear-gradient(
      to top,
      transparent 90%,
      rgba(255, 255, 255, 0.05) 100%
    );
  }

  .hero-image {
    mask-image: linear-gradient(hsl(var(--background)) 50%, transparent);
  }

  .blend-image {
    mask-image: linear-gradient(hsl(var(--background)) 70%, transparent);
  }

  .gradient-blob {
    background: radial-gradient(
        77.73% 77.73% at 98.52% 96.25%,
        #f98bda 0,
        rgba(128, 76, 239, 0.29) 50.52%,
        rgba(91, 216, 216, 0) 100%
      ),
      radial-gradient(
        141.73% 105.23% at 50% -7.16%,
        #e1f8ff 0,
        rgba(160, 198, 255, 0) 50.73%,
        rgba(162, 147, 255, 0) 100%
      ),
      radial-gradient(
        112.27% 48.54% at 1.59% 50%,
        rgba(255, 130, 227, 0.5) 0,
        rgba(255, 123, 234, 0.095) 53.91%,
        rgba(254, 216, 255, 0) 100%
      ),
      linear-gradient(153.07deg, #8177f3 6.37%, rgba(255, 230, 166, 0) 83.82%);
    background-blend-mode: normal, normal, normal, normal, normal, normal;
    border-radius: 15rem;
    filter: blur(50px);
    height: 440px;
    width: 440px;
  }

  .stencil {
    -webkit-text-stroke: 1px #fff;
    -webkit-font-smoothing: antialiased;
    color: transparent !important;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes animateParticle {
  from {
    transform: translateY(0px);
  }

  to {
    transform: translateY(-2560px);
  }
}

.swiper-wrapper {
  @apply pb-[30px];
}

.swiper-pagination-bullet {
  @apply bg-white!;
}

/*
  ---break---
*/

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
  --animate-gradient: gradient 8s linear infinite;
  @keyframes gradient {
    to {
      background-position: var(--bg-size, 300%) 0;
    }
  }
  --animate-shine: shine var(--duration) infinite linear;
  @keyframes shine {
    0% {
      background-position: 0% 0%;
    }
    50% {
      background-position: 100% 100%;
    }
    to {
      background-position: 0% 0%;
    }
  }
  --animate-shimmer-slide: shimmer-slide var(--speed) ease-in-out infinite
    alternate;
  --animate-spin-around: spin-around calc(var(--speed) * 2) infinite linear;
  @keyframes shimmer-slide {
    to {
      transform: translate(calc(100cqw - 100%), 0);
    }
  }
  @keyframes spin-around {
    0% {
      transform: translateZ(0) rotate(0);
    }
    15%,
    35% {
      transform: translateZ(0) rotate(90deg);
    }
    65%,
    85% {
      transform: translateZ(0) rotate(270deg);
    }
    100% {
      transform: translateZ(0) rotate(360deg);
    }
  }
  --animate-ripple: ripple var(--duration, 2s) ease calc(var(--i, 0) * 0.2s)
    infinite;
  @keyframes ripple {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      transform: translate(-50%, -50%) scale(0.9);
    }
  }
  @keyframes shine {
    0% {
      background-position: 0% 0%;
    }
    50% {
      background-position: 100% 100%;
    }
    to {
      background-position: 0% 0%;
    }
  }
}

/*
  ---break---
*/

:root {
  --radius: 0.625rem;
  --background: oklch(0.97 0.01 83);
  --foreground: oklch(0.15 0.01 70);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 70);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 70);
  --primary: oklch(0.71 0.19 45);
  --primary-foreground: oklch(0.98 0.005 95);
  --secondary: oklch(0.97 0.005 95);
  --secondary-foreground: oklch(0.25 0.02 70);
  --muted: oklch(0.97 0.005 95);
  --muted-foreground: oklch(0.55 0.01 75);
  --accent: oklch(0.97 0.005 95);
  --accent-foreground: oklch(0.25 0.02 70);
  --destructive: oklch(0.7 0.25 27);
  --destructive-foreground: oklch(0.98 0.005 95);
  --border: oklch(0.9 0.01 70);
  --input: oklch(0.92 0.01 70);
  --ring: oklch(0.71 0.19 45);
  --chart-1: oklch(0.65 0.22 41);
  --chart-2: oklch(0.6 0.12 184);
  --chart-3: oklch(0.4 0.07 227);
  --chart-4: oklch(0.75 0.19 85);
  --chart-5: oklch(0.77 0.19 70);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}
/*
  ---break---
*/

.dark {
  --background: oklch(0.14 0.03 265);
  --background: oklch(0.268 0.001 354);
  --foreground: oklch(0.985 0.014 300);
  --card: oklch(0.308 0.002 329);
  --card-foreground: oklch(0.985 0.014 300);
  --popover: oklch(0.236 0.003 341);
  --popover-foreground: oklch(0.985 0.014 300);
  --primary: oklch(0.617 0.137 37);
  --primary-foreground: oklch(0.985 0.014 300);
  --secondary: oklch(0.268 0.007 360);
  --secondary-foreground: oklch(0.985 0.014 300);
  --muted: oklch(0.351 0.001 9);
  --muted-foreground: oklch(0.716 0.011 348);
  --accent: oklch(0.268 0.007 360);
  --accent-foreground: oklch(0.985 0.014 300);
  --destructive: oklch(0.577 0.215 27);
  --destructive-foreground: oklch(0.985 0.014 300);
  --border: oklch(0.37 0.001 354);
  --input: oklch(0.37 0.001 354);
  --ring: oklch(0.646 0.194 41);
  --chart-1: oklch(0.53 0.207 265);
  --chart-2: oklch(0.699 0.127 169);
  --chart-3: oklch(0.723 0.149 60);
  --chart-4: oklch(0.619 0.219 312);
  --chart-5: oklch(0.612 0.213 4);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
