const Particles = ({ size }: { size: "small" | "lg" }) => {
  return (
    <div className="particles w-full h-svh z-10 absolute top-0 left-0 ">
      {[1, 2, 3].map((item) => (
        <div
          key={item}
          className={`particle-${item} rounded-full ${
            size === "small" ? "w-[1px] h-[1px]" : "w-[1.5px] h-[1.5px]"
          }`}
        ></div>
      ))}
    </div>
  );
};

export default Particles;
