import PricingDetails from "../components/pricing/PricingDetails";
import HeroDescription from "../components/common/HeroDescription";
import { Metadata } from "next";

interface SearchParams {
  type?: string;
}

export async function generateMetadata({
  searchParams,
}: {
  searchParams: SearchParams;
}): Promise<Metadata> {
  const pricingType = searchParams?.type || "yearly";

  if (pricingType === "monthly") {
    return {
      title: "Inloop Monthly Pricing Plans | AI Sales Empowerment Solutions",
      description:
        "Select the perfect monthly plan that works best for you and your team",
      keywords: "tryinloop pricing, tryinloop pricing monthly",
      openGraph: {
        title: "Inloop Monthly Plans",
        description:
          "Select the perfect monthly plan that works best for you and your team",
        url: "https://tryinloop.com/pricing?type=monthly",
        images: [
          {
            url: "https://tryinloop.com/meta-images/pricing.png",
            width: 800,
            height: 400,
          },
        ],
      },
    };
  }

  return {
    title: "Inloop Yearly Pricing Plans | AI Sales Empowerment Solutions",
    description:
      "Select the perfect yearly plan that works best for you and your team",
    keywords: "tryinloop pricing, tryinloop pricing yearly",
    openGraph: {
      title: "Inloop Yearly Plans",
      description:
        "Select the perfect yearly plan that works best for you and your team",
      url: "https://tryinloop.com/pricing?type=yearly",
      images: [
        {
          url: "https://tryinloop.com/meta-images/pricing.png",
          width: 800,
          height: 400,
        },
      ],
    },
  };
}

export default function Pricing({
  searchParams,
}: {
  searchParams: SearchParams;
}) {
  const pricingType = searchParams?.type || "yearly";
  const isYearly = pricingType !== "monthly";

  return (
    <div className="py-10 mt-26 relative">
      <HeroDescription
        headingLine1="Inloop"
        headingLine2="Plans"
        paragraph="Select the perfect plan that works best for you and your team"
      />

      <PricingDetails isYearly={isYearly} />
    </div>
  );
}
