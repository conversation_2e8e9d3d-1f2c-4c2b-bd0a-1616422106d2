"use client";

import { motion } from "framer-motion";
import { SocialPostCard } from "./social-post-card";

// Re-declare enums as they're not exported from social-post-card
enum Platform {
  TWITTER = "TWITTER",
  LINKEDIN = "LINKEDIN",
  FACEBOOK = "FACEBOOK",
  INSTAGRAM = "INSTAGRAM",
  YOUTUBE = "YOUTUBE",
  TIKTOK = "TIKTOK",
  BLOG = "BLOG",
  NEWS = "NEWS",
}

enum DataLakeType {
  POST = "POST",
  COMMENT = "COMMENT",
  ARTICLE = "ARTICLE",
}

export function SocialPostList() {
  // Use fixed dates to avoid hydration mismatch
  const dates = [
    "2025-03-20T12:00:00Z",
    "2025-02-15T08:30:00Z",
    "2024-12-10T16:45:00Z",
    "2024-11-05T09:15:00Z",
    "2025-04-28T14:20:00Z",
    "2024-10-22T11:05:00Z",
  ];

  // Dummy posts data with social plans
  const dummyPosts = [
    {
      id: "post1",
      platformPostId: "123456789",
      publishedAt: dates[0],
      platform: Platform.LINKEDIN,
      type: DataLakeType.POST,
      content:
        "Excited to announce our latest product launch! Our team has been working tirelessly to bring this innovative solution to market. #ProductLaunch #Innovation",
      author: {
        profileImage: "https://randomuser.me/api/portraits/women/12.jpg",
        name: "Sarah Johnson",
        username: "sarahj",
      },
      totalLikes: 246,
      totalComments: 42,
      totalShares: 18,
    },
    {
      id: "post2",
      platformPostId: "*********",
      publishedAt: dates[1],
      platform: Platform.TWITTER,
      type: DataLakeType.POST,
      content:
        "Just released our new case study on how we helped a Fortune 500 company increase their conversion rate by 150%. Check it out on our website! #Marketing #CaseStudy",
      author: {
        profileImage: "https://randomuser.me/api/portraits/men/32.jpg",
        name: "Michael Chen",
        username: "mchen",
      },
      totalLikes: 189,
      totalComments: 27,
      totalShares: 64,
      images: [
        "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      ],
    },
    {
      id: "post3",
      platformPostId: "*********",
      publishedAt: dates[2],
      platform: Platform.INSTAGRAM,
      type: DataLakeType.POST,
      content:
        "Behind the scenes at our company retreat! Building strong teams is at the core of our culture. Swipe to see more highlights from our amazing week together. #CompanyCulture #TeamBuilding",
      author: {
        profileImage: "https://randomuser.me/api/portraits/women/45.jpg",
        name: "Emily Rodriguez",
        username: "emilyr",
      },
      totalLikes: 583,
      totalComments: 94,
      totalShares: 12,
      images: [
        "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
        "https://images.unsplash.com/photo-1531482615713-2afd69097998?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      ],
    },
    {
      id: "post4",
      platformPostId: "*********",
      publishedAt: dates[3],
      platform: Platform.TIKTOK,
      type: DataLakeType.POST,
      content: "If you're happy and you know it clap your hands 👏 #HappyVibes #FridayFeeling",
      author: {
        profileImage: "https://randomuser.me/api/portraits/women/36.jpg",
        name: "Jessica Taylor",
        username: "jesst",
      },
      totalLikes: 1246,
      totalComments: 142,
      totalShares: 318,
    },
    {
      id: "post5",
      platformPostId: "321654987",
      publishedAt: dates[4],
      platform: Platform.FACEBOOK,
      type: DataLakeType.POST,
      content:
        "We're thrilled to announce that we've been recognized as one of the 'Top 50 Places to Work' for the third year in a row! This achievement is a testament to our incredible team and the culture we've built together.",
      author: {
        profileImage: "https://randomuser.me/api/portraits/men/67.jpg",
        name: "David Wilson",
        username: "davidw",
      },
      totalLikes: 872,
      totalComments: 156,
      totalShares: 204,
      images: [
        "https://images.unsplash.com/photo-1565843714144-d5a3292ae82d?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
      ],
    },
    {
      id: "post6",
      platformPostId: "789456123",
      publishedAt: dates[5],
      platform: Platform.YOUTUBE,
      type: DataLakeType.POST,
      content:
        "Watch our latest webinar on 'The Future of AI in Business' featuring industry experts discussing practical applications and future trends. Don't forget to subscribe for more content! #AI #BusinessStrategy #Webinar",
      author: {
        profileImage: "https://randomuser.me/api/portraits/women/28.jpg",
        name: "Alexandra Kim",
        username: "alexk",
      },
      totalLikes: 326,
      totalComments: 83,
      totalShares: 127,
      videos: ["https://example.com/video-placeholder.mp4"],
    },
  ];

  return (
    <>
      <div className="columns-1 md:columns-2 lg:columns-3 gap-4">
        {dummyPosts.map((post, index) => (
          <div key={post.id} className="py-2 h-full flex flex-col break-inside-avoid">
            <motion.div 
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
            >
              <SocialPostCard {...post} />
            </motion.div>
          </div>
        ))}
      </div>
    </>
  );
}
