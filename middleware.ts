import { NextResponse, NextRequest } from "next/server";

function unauthorizedResponse(message: string) {
  return new NextResponse(message, {
    status: 401,
    headers: {
      "WWW-Authenticate": 'Basic realm="user_pages"',
    },
  });
}

export function middleware(request: NextRequest) {
  const auth = request.headers.get("authorization");

  if (!auth) {
    return unauthorizedResponse("Authentication required");
  }

  const [scheme, encoded] = auth.split(" ");

  if (scheme !== "Basic" || !encoded) {
    return unauthorizedResponse("Invalid authentication format");
  }

  const buffer = Buffer.from(encoded, "base64");
  const [username, password] = buffer.toString().split(":");

  if (username !== "tryinloop" || password !== "noah") {
    return unauthorizedResponse("Invalid credentials");
  }

  return NextResponse.next();
}

export const config = {
  matcher: "/noah",
};
