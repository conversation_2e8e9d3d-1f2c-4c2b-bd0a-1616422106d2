"use client";

import HeroVideoDialog from "@/components/magicui/hero-video-dialog";
import { useScroll, useTransform } from "framer-motion";
import { useRef } from "react";
import "swiper/css";
import "swiper/css/effect-fade";
import BackgroundGrid from "./hero/BackgroundGrid";
import GridLines from "./hero/GridLines";
import HeroContent from "./hero/HeroContent";
import TrustedBySection from "./hero/TrustedBySection";

interface HeroProps {
  headingLine1?: string;
  headingLine2?: string;
  paragraph?: string;
  trustedBySectionHeading?: string;
}

// Main Hero Component
export default function Hero({
  headingLine1,
  headingLine2,
  paragraph,
  trustedBySectionHeading,
}: HeroProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll({ target: containerRef });

  // Parallax effects
  const headingY = useTransform(scrollY, [0, 300], [0, -30]);

  return (
    <div
      className="relative overflow-hidden bg-linear-to-b from-background via-background/95 to-background/90"
      ref={containerRef}
    >
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none"></div>

      <BackgroundGrid />
      <GridLines />

      <div className="flex flex-col items-center min-h-[750px] relative">
        <HeroContent
          headingY={headingY}
          headingLine1={headingLine1}
          headingLine2={headingLine2}
          paragraph={paragraph}
        />

        <HeroVideoDialog
          className="block max-w-4xl"
          animationStyle="from-center"
          videoSrc="https://www.youtube.com/embed/LU4-hsbFhp8"
          thumbnailSrc="/images/hero-3.png"
          thumbnailSrcDark="/images/hero-dark.png"
          thumbnailAlt="Hero Video"
        />
      </div>

      <TrustedBySection heading={trustedBySectionHeading} />
    </div>
  );
}
