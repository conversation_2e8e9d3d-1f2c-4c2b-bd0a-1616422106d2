"use client";
import { FreeMode, Pagination, Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import CustomButton from "./common/CustomButton";
import { useState } from "react";
import { motion } from "framer-motion";

import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";
import { FlameIcon, GraduationCap, ShieldCheck } from "lucide-react";

const options = [
  { id: "direct", label: "Direct", icon: <FlameIcon className="w-4 h-4" /> },
  {
    id: "professional",
    label: "Professional",
    icon: <GraduationCap className="w-4 h-4" />,
  },
  {
    id: "sincere",
    label: "Sincere",
    icon: <ShieldCheck className="w-4 h-4" />,
  },
];

const valuesData = [
  {
    img: "/images/value-evolve.png",
    title: "Continuously evolving",
    description:
      "Continuously evolves by learning, refining knowledge, and enhancing efficiency with each engagement",
  },
  {
    img: "/images/value-tailored.png",
    title: "Tailored for you",
    description:
      "Retain valuable insights and apply past learnings to improve future outcomes",
  },
  {
    img: "/images/value-ai.png",
    title: "Autonomous Intelligence",
    description:
      "Independent, proactive, and able to execute complex tasks without supervision to drive results autonomously.",
  },
];

const Values = () => {
  const [selected, setSelected] = useState("direct");
  const [email, setEmail] = useState("");
  const [activeIndex, setActiveIndex] = useState(1);

  return (
    <section className="mt-36 max-w-6xl m-auto relative">
      {/* Background decorative elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-primary/5 rounded-full blur-[120px] -translate-x-1/2" />
        <div className="absolute bottom-0 right-1/4 w-[500px] h-[500px] bg-secondary/5 rounded-full blur-[120px] translate-x-1/2" />
      </div>

      <motion.div
        className="sm:text-center relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="relative inline-block">
          <h2 className="mb-4 text-4xl sm:text-5xl leading-tight font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground via-foreground/90 to-foreground/80">
            AI Colleague to Supercharge your Sales Team
          </h2>
        </div>

        <motion.p
          className="m-auto text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed sm:max-w-2xl"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Inloop AI Sales Influencer and AI Sales Development Representatives
          integrate seamlessly into your workflows, automating, optimizing
          outreach, enabling your sales team to scale effortlessly and win more.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="relative inline-block group mt-8"
        >
          <div className="absolute -inset-1 bg-linear-to-r from-primary/60 via-secondary/60 to-primary/60 rounded-full blur-sm opacity-30 group-hover:opacity-100 transition duration-300" />
          <CustomButton href="https://calendly.com/sahil-tryinloop/inloop-demo" className="relative">
            Get Started
          </CustomButton>
        </motion.div>
      </motion.div>

      <motion.div
        className="mt-20 relative"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, delay: 0.8 }}
      >
        <Swiper
          breakpoints={{
            320: { slidesPerView: 1.2, spaceBetween: 20 },
            768: { slidesPerView: 2.2, spaceBetween: 30 },
            1280: { slidesPerView: 2.5, spaceBetween: 40 },
          }}
          centeredSlides={true}
          pagination={{
            clickable: true,
            bulletActiveClass: "bg-primary/70 w-8",
            bulletClass:
              "inline-block h-1 w-4 bg-primary/30 rounded-full transition-all duration-300 mx-1",
          }}
          modules={[FreeMode, Pagination, Autoplay]}
          autoplay={{
            delay: 2000,
            disableOnInteraction: false,
          }}
          className="mt-8 pb-12!"
          initialSlide={activeIndex}
          onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
        >
          {valuesData.map((d, idx) => (
            <SwiperSlide key={idx}>
              <motion.div
                className={cn(
                  "p-4 transition-all duration-500 rounded-[24px]",
                  activeIndex === idx
                    ? "opacity-100 scale-[1.02]"
                    : "opacity-50 scale-95"
                )}
                whileHover={{ scale: activeIndex === idx ? 1.03 : 0.98 }}
              >
                <div className="relative rounded-[20px] overflow-hidden aspect-video">
                  <div className="absolute -inset-[1px] bg-linear-to-b from-primary/20 via-secondary/10 to-primary/20 rounded-[20px]" />
                  <div className="absolute inset-[1px] rounded-[19px] overflow-hidden">
                    {/* <video
                      className="w-full h-full object-cover"
                      src={d.video}
                      muted
                      autoPlay
                      loop
                      playsInline
                      poster="/logo.png"
                    >
                      <source src={d.video} type="video/mp4" />
                    </video> */}
                    <img
                      src={d.img}
                      alt={d.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-linear-to-t from-background/40 via-background/20 to-transparent pointer-events-none" />
                  </div>
                </div>
                <motion.div
                  className="mt-6"
                  initial={false}
                  animate={{
                    opacity: activeIndex === idx ? 1 : 0.7,
                    y: activeIndex === idx ? 0 : 5,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <h3 className="text-lg sm:text-2xl xl:text-3xl font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
                    {d.title}
                  </h3>
                  <p className="text-base mt-2 text-muted-foreground">
                    {d.description}
                  </p>
                </motion.div>
              </motion.div>
            </SwiperSlide>
          ))}
        </Swiper>
      </motion.div>

      <motion.div
        className="relative mt-36 rounded-[32px] overflow-hidden shadow-xs"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-linear-to-br from-background/80 via-background/60 to-background/80 backdrop-blur-xl border border-primary/10 rounded-[32px] shadow-[0_4px_20px_rgba(0,0,0,0.1)]" />

        {/* <div className="relative grid lg:grid-cols-2 gap-12 p-8 sm:p-12">
          <motion.form
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-8"
          >
            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <h2 className="mb-4 text-4xl sm:text-5xl leading-tight font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground via-foreground/90 to-foreground/80">
                  Get an email from William
                </h2>
                <p className="text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed">
                  Enter your email and get a customized AI powered Social Post
                  from William. Engage with him to learn more!
                </p>
              </motion.div>
            </div>

            <div className="space-y-8">
              <div className="space-y-4">
                <label
                  htmlFor="voice"
                  className="text-sm font-medium text-muted-foreground/90 flex items-center gap-2"
                >
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs">
                    1
                  </span>
                  Select Tone of Voice
                </label>
                <div className="flex flex-wrap gap-3">
                  {options.map((option) => (
                    <label key={option.id} className="cursor-pointer">
                      <input
                        type="radio"
                        name="tone"
                        value={option.id}
                        checked={selected === option.id}
                        onChange={() => setSelected(option.id)}
                        className="hidden"
                        required
                      />
                      <motion.div
                        className={cn(
                          "flex items-center gap-2 px-5 py-2.5 rounded-full border transition-all duration-300",
                          selected === option.id
                            ? "bg-linear-to-r from-primary/80 to-primary/60 border-primary/50 shadow-lg shadow-primary/20 scale-[1.02]"
                            : "border-primary/20 text-muted-foreground hover:border-primary/40 hover:bg-primary/5"
                        )}
                        whileHover={{
                          scale: selected === option.id ? 1.02 : 1.05,
                        }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span
                          className={cn(
                            "transition-colors duration-300",
                            selected === option.id
                              ? "text-primary-foreground"
                              : "text-primary/60"
                          )}
                        >
                          {option.icon}
                        </span>
                        <span
                          className={cn(
                            "font-medium transition-colors duration-300",
                            selected === option.id
                              ? "text-primary-foreground"
                              : "text-muted-foreground"
                          )}
                        >
                          {option.label}
                        </span>
                      </motion.div>
                    </label>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-muted-foreground/90 flex items-center gap-2"
                >
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs">
                    2
                  </span>
                  Enter your email address
                </label>
                <div className="relative group">
                  <div className="absolute -inset-0.5 rounded-full bg-linear-to-r from-primary/50 via-secondary/50 to-primary/50 opacity-0 group-hover:opacity-20 group-focus-within:opacity-20 transition duration-300" />
                  <input
                    placeholder="<EMAIL>"
                    id="email"
                    name="email"
                    type="email"
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="px-6 py-3.5 rounded-full w-full bg-background/50 border border-primary/20 focus:border-primary/40 focus:outline-hidden text-foreground placeholder:text-muted-foreground/50 transition-all duration-300 relative shadow-[0_2px_10px_rgba(0,0,0,0.05)] focus:shadow-[0_2px_20px_rgba(0,0,0,0.1)]"
                  />
                </div>
              </div>

              <motion.button
                disabled={!email}
                type="submit"
                className={cn(
                  buttonVariants({ size: "lg" }),
                  "rounded-full h-12 px-8 font-medium w-full relative group",
                  "bg-linear-to-r from-primary/90 to-primary/80 hover:from-primary hover:to-primary/90 text-primary-foreground",
                  "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-primary/90 disabled:hover:to-primary/80",
                  "shadow-[0_2px_10px_rgba(0,0,0,0.1)] hover:shadow-[0_2px_20px_rgba(0,0,0,0.15)]"
                )}
                whileHover={{ scale: email ? 1.02 : 1 }}
                whileTap={{ scale: email ? 0.98 : 1 }}
              >
                <span className="relative z-10">Email Me</span>
                <div className="absolute inset-0 rounded-full bg-linear-to-r from-primary/80 via-secondary/80 to-primary/80 opacity-0 group-hover:opacity-20 transition duration-300" />
              </motion.button>
            </div>
          </motion.form>

          <motion.div
            className="hidden lg:block relative"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="absolute -inset-4 bg-linear-to-br from-primary/10 via-secondary/5 to-accent/10 rounded-3xl opacity-50" />
            <div className="absolute inset-0 bg-linear-to-br from-background/40 via-transparent to-background/40 backdrop-blur-xs rounded-2xl" />
            <motion.div
              className="relative rounded-2xl overflow-hidden shadow-[0_4px_20px_rgba(0,0,0,0.1)] group"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.4 }}
            >
              <div className="absolute inset-0 bg-linear-to-br from-primary/5 via-secondary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <img
                src="/images/william-email.png"
                className="relative w-full h-full object-contain rounded-2xl"
                alt="Email preview"
              />
              <div className="absolute inset-0 border border-primary/10 rounded-2xl" />
            </motion.div>
          </motion.div>
        </div> */}
      </motion.div>
    </section>
  );
};

export default Values;
