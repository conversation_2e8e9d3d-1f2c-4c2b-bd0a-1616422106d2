import { useState } from "react";

/**
 * Hook for managing content expansion and truncation
 */
export function useContentManagement(content: string, maxParagraphs = 10) {
  const [expanded, setExpanded] = useState(false);

  // Define limits
  const MAX_CHARS = 260;
  const MAX_PARAGRAPHS = maxParagraphs;

  // Split content into paragraphs
  const paragraphs = content.includes("\\n")
    ? content.split("\\n")
    : content.split("\n");

  // Calculate total text length and determine if content is long
  const contentText = paragraphs.join("\n"); // Join with actual newlines
  const hasLongContent =
    contentText.length > MAX_CHARS || paragraphs.length > MAX_PARAGRAPHS;

  // Process content for display - either truncate or show full content
  const getDisplayContent = () => {
    if (!hasLongContent || expanded) {
      return paragraphs;
    }

    // First check paragraph count - if we have more than MAX_PARAGRAPHS, truncate by paragraphs
    if (paragraphs.length > MAX_PARAGRAPHS) {
      // Get first MAX_PARAGRAPHS and add ellipsis to the last one
      const result = paragraphs.slice(0, MAX_PARAGRAPHS);
      if (result.length > 0) {
        const lastParagraph = result[result.length - 1];
        result[result.length - 1] = lastParagraph + "...";
      }
      return result;
    }

    // Otherwise truncate by character count
    let charCount = 0;
    let truncatedParagraphs = [];
    let addedEllipsis = false;

    for (const paragraph of paragraphs) {
      // Check if adding this paragraph would exceed the limit
      if (charCount + paragraph.length > MAX_CHARS) {
        // If we're already near the limit, just add ellipsis to last paragraph
        if (charCount > MAX_CHARS * 0.7) {
          if (truncatedParagraphs.length > 0) {
            // Add ellipsis to the last paragraph already added
            const lastIndex = truncatedParagraphs.length - 1;
            truncatedParagraphs[lastIndex] =
              truncatedParagraphs[lastIndex] + "...";
            addedEllipsis = true;
          }
          break;
        }

        // Otherwise add a portion of this paragraph
        const remainingChars = MAX_CHARS - charCount;
        const truncatedParagraph =
          paragraph.substring(0, remainingChars) + "...";
        truncatedParagraphs.push(truncatedParagraph);
        addedEllipsis = true;
        break;
      }

      truncatedParagraphs.push(paragraph);
      charCount += paragraph.length + 1; // +1 for the newline
    }

    // If we haven't added ellipsis yet but truncated the content, add to last paragraph
    if (
      !addedEllipsis &&
      truncatedParagraphs.length > 0 &&
      truncatedParagraphs.length < paragraphs.length
    ) {
      const lastIndex = truncatedParagraphs.length - 1;
      truncatedParagraphs[lastIndex] = truncatedParagraphs[lastIndex] + "...";
    }

    return truncatedParagraphs;
  };

  return {
    expanded,
    setExpanded,
    hasLongContent,
    displayParagraphs: getDisplayContent(),
    title: paragraphs.join(" ").substring(0, 100),
  };
}
