import { cn } from "@/lib/utils";

const FeatureCard = ({
  bg,
  className,
  title,
  description,
  imageUrl,
  imageClassName,
}: {
  bg: string;
  className?: string;
  title: string;
  description: string | string[];
  imageUrl: string;
  imageClassName?: string;
}) => {
  return (
    <div
      className={cn(
        "relative sm:sticky transition-all duration-500 hover:shadow-md top-20 group border  bg-background/90 backdrop-blur-xl p-4 sm:p-10 shadow-sm rounded-3xl sm:h-[600px] overflow-hidden",
        className
      )}
    >
      <div className="absolute left-0 top-0 w-full h-full opacity-60 transition-all duration-500 group-hover:opacity-100">
        <img
          src={bg}
          className="w-full h-full object-cover object-center rounded-3xl"
        />
      </div>

      <div className="text-center sm:absolute sm:-bottom-4 sm:right-10 z-10">
        <img
          src={imageUrl}
          className={cn(imageClassName, "sm:w-[500px] blend-image")}
        />
      </div>
      <div className="whitespace-pre absolute left-20 top-[40%] opacity-[.07] transition-all duration-500 group-hover:-translate-x-44 hidden sm:block">
        <h1 className="stencil text-[200px] font-bold">{title}</h1>
      </div>

      <div className="relative z-20">
        <h1 className="mb-4 text-3xl sm:text-4xl font-medium">{title}</h1>
        {Array.isArray(description) ? (
          <ul className="text-base sm:text-lg font-normal text-gray-400 sm:leading-relaxed md:leading-relaxed list-disc space-y-2 ml-3 pb-2">
            {description.map((item, key) => (
              <li key={key}>{item}</li>
            ))}
          </ul>
        ) : (
          <p className="text-base sm:text-lg font-normal text-gray-400 sm:leading-relaxed md:leading-relaxed">
            {description}
          </p>
        )}
      </div>
    </div>
  );
};

export default FeatureCard;
