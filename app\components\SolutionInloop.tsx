"use client";

import { motion } from "framer-motion";
import CustomButton from "./common/CustomButton";
import HowItWorks from "./HowItWorks";

const Solution = () => {
  return (
    <section className="mt-16 max-w-6xl m-auto relative">
      {/* Background decorative elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-[128px] -translate-x-1/2" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-[128px] translate-x-1/2" />
      </div>

      <motion.div
        className="sm:text-center relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <motion.div
          className="relative inline-block"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="mb-4 text-4xl sm:text-5xl leading-tight font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground via-foreground/90 to-foreground/80">
            AI Colleagues for Ambitious Sales Team
          </h2>
        </motion.div>

        <motion.p
          className="m-auto text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed sm:max-w-3xl"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          AI Colleagues effortlessly integrate with your sales team, automating
          thought leadership, optimizing workflows, and accelerating your GTM
          strategy.
        </motion.p>

        <motion.p
          className="m-auto text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed sm:max-w-3xl"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          With multiple content format and trending suggestions, they boost
          efficiency—so your team can focus on building trust, closing deals and
          driving growth.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="relative inline-block group mt-8"
        >
          <div className="absolute -inset-1 bg-linear-to-r from-primary/60 via-secondary/60 to-primary/60 rounded-full blur-sm opacity-30 group-hover:opacity-100 transition duration-300" />
          <CustomButton
            href="https://calendly.com/sahil-tryinloop/inloop-demo"
            className="relative"
          >
            Get Started
          </CustomButton>
        </motion.div>
      </motion.div>

      <motion.div
        className="mt-20 grid [grid-template-columns:repeat(auto-fit,minmax(300px,1fr))] gap-8 md:gap-20"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, delay: 0.8 }}
      >
        <SolutionCard
          imgSrc="/agents/william-1.png"
          heading="William - AI Sales Influencer"
          paragraph="William turns your sales team into industry thought leaders, crafting compelling content—podcasts, articles, and posts—to drive engagement and deals"
          buttonLink="https://calendly.com/sahil-tryinloop/inloop-demo"
          buttonText="Hire William"
          delay={0}
        />
        <SolutionCard
          imgSrc="/agents/ines-1.png"
          heading="Ines - AI Sales Rep"
          paragraph="Ines transforms your market into your revenue. She engages prospects across channels, driving qualified meetings and building pipeline."
          buttonLink="https://calendly.com/sahil-tryinloop/inloop-demo"
          buttonText="Hire Ines"
          delay={0.2}
          comingSoon={true}
        />
      </motion.div>

      <HowItWorks />
    </section>
  );
};

interface SolutionCardProps {
  imgSrc: string;
  heading: string;
  paragraph: string;
  buttonLink: string;
  buttonText: string;
  delay: number;
  comingSoon?: boolean;
}

const SolutionCard = (props: SolutionCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, delay: props.delay }}
      className="group relative"
    >
      {props.comingSoon && (
        <div className="absolute top-4 right-4 p-2 bg-linear-to-r from-background/95 to-background/80 border border-border/30 rounded-lg shadow-lg backdrop-blur-xs z-20">
          <div className="text-sm font-mono text-primary">Coming Soon</div>
        </div>
      )}
      <div className="relative max-w-xl mx-auto">
        <motion.div
          className="absolute -inset-2 rounded-[28px] bg-linear-to-br from-primary/20 via-secondary/10 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 0.5 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: props.delay + 0.2 }}
        />
        <div className="aspect-square rounded-[26px] overflow-hidden relative">
          <div className="absolute inset-0 bg-linear-to-t from-background via-background/20 to-transparent opacity-0 group-hover:opacity-40 transition-opacity duration-500" />
          <motion.img
            src={props.imgSrc}
            alt={props.heading}
            className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
            initial={{ scale: 1.2 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 1.2, delay: props.delay }}
          />
          <div className="absolute inset-0 border border-primary/10 rounded-[26px]" />
        </div>
      </div>

      <motion.div
        className="mt-8 relative max-w-xl mx-auto"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, delay: props.delay + 0.4 }}
      >
        <h3 className="text-lg sm:text-2xl xl:text-3xl bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
          {props.heading}
        </h3>
        <hr className="my-4 border-primary/10" />
        <p className="text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed">
          {props.paragraph}
        </p>
        <motion.div
          className="relative inline-block group mt-6"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <div className="absolute -inset-1 bg-linear-to-r from-primary/40 via-secondary/40 to-primary/40 rounded-full blur-sm opacity-30 group-hover:opacity-100 transition duration-300" />
          <CustomButton
            href={props.buttonLink}
            className="relative"
            disabled={props.comingSoon}
          >
            {props.comingSoon ? "Coming Soon" : props.buttonText}
          </CustomButton>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default Solution;
