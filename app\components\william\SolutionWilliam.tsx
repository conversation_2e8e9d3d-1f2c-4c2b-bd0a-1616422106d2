"use client";

import { motion } from "framer-motion";
import CustomButton from "../common/CustomButton";
import { SocialPostList } from "@/components/social-post-list";
import WhyHireWilliam from "./WhyHireWilliam";
import { Check, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";
import Meta from "../meta";
import { useGtmTrack } from "@/lib/tracking";
const Solution = ({
  heading = "Linkedin Brand Building for Ambitious Professionals",
  whyWilliamSubHeading = "<PERSON> can co-create LinkedIn Content with you or take full ownership, delivering a week’s worth of thought leadership content tailored to your voice, audience, and engagement goals.",
}: {
  heading?: string;
  whyWilliamSubHeading?: string;
}) => {
  const { getStartedTrack } = useGtmTrack();
  // Add reduced motion state for mobile/Safari optimization
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  // Check for reduced motion preference on component mount
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = () => setPrefersReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);

    // Detect Safari for specific optimizations
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (isSafari || isMobile) {
      setPrefersReducedMotion(true);
    }

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  // Optimized animation settings
  const animationSettings = {
    initial: prefersReducedMotion ? { opacity: 0.5 } : { opacity: 0, y: 20 },
    whileInView: prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 },
    viewport: { once: true, margin: "0px 0px -100px 0px" },
    transition: { duration: prefersReducedMotion ? 0.4 : 0.8 },
  };

  return (
    <section className="py-12 sm:py-20 container px-0 sm:px-6 m-auto relative">
      <motion.div className="sm:text-center relative" {...animationSettings}>
        <Meta
          heading={heading}
          subheading="William acts as personal Linkedin Content Strategist for you and your team. He automates your Linkedin Content Pipeline with engaging thought leadership content."
          iconText="7x engagement within the first month!"
        />

        <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-3 mt-6 sm:mt-8 mb-6 sm:mb-8">
          {[
            "Template Library",
            "Topical Content",
            "Inspiration Hub",
            "Content Calendar",
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full bg-background/80 border border-border/50 text-xs sm:text-sm text-muted-foreground backdrop-blur-sm"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 + index * 0.1 }}
            >
              <Check className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary" />{" "}
              {feature}
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="relative flex flex-col sm:inline-flex sm:flex-row gap-2 sm:gap-4 mt-2 w-full sm:w-auto justify-center"
        >
          <div className="relative group mb-2 sm:mb-0 w-full sm:w-auto mx-4 sm:mx-0">
            <div className="absolute -inset-1 bg-gradient-to-r from-primary/60 via-secondary/60 to-primary/60 rounded-full blur-sm opacity-70 group-hover:opacity-100 transition duration-300" />
            <CustomButton
              href="https://william.tryinloop.com"
              target="_blank"
              onClick={getStartedTrack}
              className="relative text-white px-4 sm:px-6 w-full sm:w-auto"
            >
              Get Started <ChevronRight className="ml-1 h-4 w-4" />
            </CustomButton>
          </div>
        </motion.div>
      </motion.div>

      {/* Social post list container with applied class for the card gradients */}
      <motion.div
        className="mt-16 px-4 sm:px-6 relative"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {/* Simple background for the container */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-background/50 to-background rounded-3xl blur-sm"></div>
        </div>

        {/* Social post list with custom class for card styling */}
        <div className="social-post-container py-6 rounded-3xl">
          <SocialPostList />
        </div>
      </motion.div>

      <motion.div
        className="mt-24"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Meta
          heading="Why Choose William?"
          subheading={whyWilliamSubHeading}
          iconText="Best AI Agent for Linkedin Content"
        />

        <WhyHireWilliam />
      </motion.div>
    </section>
  );
};

export default Solution;
