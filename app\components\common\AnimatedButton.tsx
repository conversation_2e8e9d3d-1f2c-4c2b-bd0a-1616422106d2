"use client";

import { cn } from "@/lib/utils";
import { BorderBeam } from "@/components/magicui/border-beam";
import { ArrowRight } from "lucide-react";
import {
  ButtonHTMLAttributes,
  ReactNode,
  AnchorHTMLAttributes,
  MouseEventHandler,
} from "react";
import { VariantProps, cva } from "class-variance-authority";
import Link from "next/link";

const buttonVariants = cva(
  "group relative inline-flex items-center gap-2 rounded-full border border-primary/50 bg-primary/5 font-medium text-primary shadow-sm backdrop-blur-sm transition-all duration-300 hover:bg-primary/10 hover:shadow-primary/20 active:translate-y-0.5 cursor-pointer",
  {
    variants: {
      size: {
        sm: "text-xs px-4 py-1.5",
        default: "text-sm px-6 py-2",
        md: "px-8 py-3",
        lg: "text-md px-10 py-3.5",
      },
      fullWidth: {
        true: "w-full justify-center",
        false: "",
      },
      variant: {
        default: "",
        badge: "absolute -translate-x-1/2 -translate-y-1/2",
      },
    },
    defaultVariants: {
      size: "default",
      fullWidth: false,
      variant: "default",
    },
  }
);

type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement>;
type AnchorProps = AnchorHTMLAttributes<HTMLAnchorElement>;

interface AnimatedButtonProps
  extends Omit<ButtonProps, keyof AnchorProps>,
    VariantProps<typeof buttonVariants> {
  children: ReactNode;
  showArrow?: boolean;
  beamSize?: number;
  beamDuration?: number;
  beamColorFrom?: string;
  beamColorTo?: string;
  className?: string;
  position?: {
    top?: string;
    left?: string;
    right?: string;
    bottom?: string;
  };
  href?: string;
  target?: string;
  onClick?: MouseEventHandler<HTMLButtonElement | HTMLAnchorElement>;
}

export default function AnimatedButton({
  children,
  className,
  size = "default",
  fullWidth,
  variant,
  showArrow = false,
  beamSize = 40,
  beamDuration = 3.5,
  beamColorFrom = "#9c40ff",
  beamColorTo = "#ffaa40",
  position,
  href,
  target,
  onClick,
  ...props
}: AnimatedButtonProps) {
  const positionStyles = position
    ? {
        top: position.top,
        left: position.left,
        right: position.right,
        bottom: position.bottom,
      }
    : {};

  // Ensure size is one of the valid values
  const validSize =
    size && ["sm", "default", "md", "lg"].includes(size as string)
      ? size
      : "default";

  const commonClassNames = cn(
    buttonVariants({ size: validSize, fullWidth, variant }),
    "cursor-pointer",
    className
  );

  const content = (
    <>
      <span>{children}</span>

      {showArrow && (
        <ArrowRight className="h-4 w-4 opacity-70 transition-transform duration-300 group-hover:translate-x-1" />
      )}

      {/* Border Beam Effect */}
      <BorderBeam
        size={beamSize}
        duration={beamDuration}
        colorFrom={beamColorFrom}
        colorTo={beamColorTo}
      />

      {/* Background subtle glow */}
      <div className="absolute inset-0 -z-10 rounded-full bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 opacity-0 blur-xl transition-opacity duration-500 group-hover:opacity-100"></div>
    </>
  );

  if (href) {
    return (
      <Link
        href={href}
        className={commonClassNames}
        target={target || "_blank"}
        rel={target === "_blank" ? "noopener noreferrer" : undefined}
        style={positionStyles}
        onClick={onClick as MouseEventHandler<HTMLAnchorElement>}
        {...(props as AnchorProps)}
      >
        {content}
      </Link>
    );
  }

  return (
    <button
      className={commonClassNames}
      style={positionStyles}
      onClick={onClick}
      {...(props as ButtonProps)}
    >
      {content}
    </button>
  );
}
