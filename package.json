{"name": "inloop-landing", "version": "0.2.1", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/third-parties": "^15.3.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-slot": "^1.0.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.3", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "geist": "^1.3.1", "gray-matter": "^4.0.3", "lucide-react": "^0.309.0", "motion": "^12.9.2", "next": "14.0.4", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-heading-id": "^1.0.1", "remark-html": "^16.0.1", "remark-rehype": "^11.1.2", "swiper": "^11.2.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/remark-heading-id": "^1.0.0", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^4.1.4", "typescript": "^5"}}