import { useContentManagement } from "@/app/hooks/use-content-management";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import {
  Award,
  ChevronDown,
  Heart,
  LightbulbIcon,
  MessageCircle,
  PartyPopper,
  Share2,
  Smile,
  ThumbsUp,
  Wand2,
} from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Button } from "./ui/button";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "./ui/hover-card";

enum Platform {
  TWITTER = "TWITTER",
  LINKEDIN = "LINKEDIN",
  FACEBOOK = "FACEBOOK",
  INSTAGRAM = "INSTAGRAM",
  YOUTUBE = "YOUTUBE",
  TIKTOK = "TIKTOK",
  BLOG = "BLOG",
  NEWS = "NEWS",
  // Add other platforms as needed
}

enum DataLakeType {
  POST = "POST",
  COMMENT = "COMMENT",
  ARTICLE = "ARTICLE",
  // Add other types as needed
}

interface Author {
  profileImage?: string | null;
  name?: string | null;
  username?: string | null;
  platformUserId?: string; // Added based on DTO
}

interface DataLakeResponse {
  id?: string; // Assuming an ID field exists, though not in CreateInput
  platformPostId: string;
  publishedAt: string; // or Date
  platform: Platform;
  type: DataLakeType;
  content: string;
  platformPostUrl?: string | null;
  images?: string[] | null;
  videos?: string[] | null;
  author?: Author | null; // Use the Author interface
  totalLikes?: number | null;
  totalPraises?: number | null;
  totalInterests?: number | null;
  totalAppreciations?: number | null;
  totalEmpathies?: number | null;
  totalEntertainments?: number | null;
  totalShares?: number | null;
  totalComments?: number | null;
  keywords?: string[] | null;
  // Fields from the DTO's DataLakeResponse extension
  totalWords?: number;
  totalCharacters?: number;
}

interface SocialPostCardProps extends DataLakeResponse {
  hideRepurposeButton?: boolean;
  className?: string;
}

export function SocialPostCard(props: SocialPostCardProps) {
  const {
    id,
    platform,
    content,
    publishedAt,
    author,
    totalLikes = 0,
    totalComments = 0,
    totalShares = 0,
    images,
    videos,
    type,
    hideRepurposeButton = false,
    className,
  } = props;

  // Dialog state
  const [repurposeDialogOpen, setRepurposeDialogOpen] = useState(false);

  // Use the content management hook
  const { expanded, setExpanded, hasLongContent, displayParagraphs } =
    useContentManagement(content);

  // Format large numbers with K for thousands
  const formatNumber = (num: number | null | undefined): string | number => {
    if (num === null || num === undefined) return 0; // Handle null/undefined
    return num >= 1000 ? `${(num / 1000).toFixed(1)}K` : num;
  };

  // Determine if we have media (image or video)
  const hasMedia =
    (images && images.length > 0) || (videos && videos.length > 0);

  // Get the first media item and ensure it's a string
  const firstImage = images && images.length > 0 ? images[0] : undefined;
  const mediaType = firstImage ? "image" : "";

  const isNews = platform === Platform.NEWS || platform === Platform.BLOG;
  const isLinkedIn = platform === Platform.LINKEDIN;

  // Generate mock reaction data for LinkedIn
  const currentTotalLikes = totalLikes ?? 0; // Use 0 if totalLikes is null/undefined
  const reactions = isLinkedIn
    ? {
        like: Math.floor(currentTotalLikes * 0.6),
        celebrate: Math.floor(currentTotalLikes * 0.1),
        support: Math.floor(currentTotalLikes * 0.1),
        love: Math.floor(currentTotalLikes * 0.1),
        insightful: Math.floor(currentTotalLikes * 0.05),
        funny: Math.floor(currentTotalLikes * 0.05),
      }
    : undefined;

  // Get top 3 reactions by count if LinkedIn
  const topReactions = reactions
    ? Object.entries(reactions)
        .filter(([_, count]) => count && count > 0)
        .sort(([_, countA], [__, countB]) => (countB || 0) - (countA || 0))
        .slice(0, 3)
    : [];

  // Reaction icons mapping with filled styles
  const reactionIcons = {
    like: (
      <div className="flex items-center justify-center w-4 h-4 bg-[#0A66C2] text-white rounded-full shadow-md border border-white/20 bg-gradient-to-br from-[#0A66C2] to-[#004182]">
        <ThumbsUp className="h-2.5 w-2.5 fill-current" />
      </div>
    ),
    celebrate: (
      <div className="flex items-center justify-center w-4 h-4 bg-[#FAD85E] text-[#5F3C03] rounded-full shadow-md border border-white/20 bg-gradient-to-br from-[#FAD85E] to-[#E7B414]">
        <PartyPopper className="h-2.5 w-2.5 fill-current" />
      </div>
    ),
    support: (
      <div className="flex items-center justify-center w-4 h-4 bg-[#6DAE4F] text-white rounded-full shadow-md border border-white/20 bg-gradient-to-br from-[#6DAE4F] to-[#4D8F2F]">
        <Award className="h-2.5 w-2.5 fill-current" />
      </div>
    ),
    funny: (
      <div className="flex items-center justify-center w-4 h-4 bg-[#F5B728] text-[#5F3C03] rounded-full shadow-md border border-white/20 bg-gradient-to-br from-[#F5B728] to-[#E09C08]">
        <Smile className="h-2.5 w-2.5 fill-current" />
      </div>
    ),
    love: (
      <div className="flex items-center justify-center w-4 h-4 bg-[#DF704D] text-white rounded-full shadow-md border border-white/20 bg-gradient-to-br from-[#DF704D] to-[#CC4A25]">
        <Heart className="h-2.5 w-2.5 fill-current" />
      </div>
    ),
    insightful: (
      <div className="flex items-center justify-center w-4 h-4 bg-[#9C27B0] text-white rounded-full shadow-md border border-white/20 bg-gradient-to-br from-[#9C27B0] to-[#7B1FA2]">
        <LightbulbIcon className="h-2.5 w-2.5 fill-current" />
      </div>
    ),
  };

  // Format the date
  const formattedDate = formatDistanceToNow(new Date(publishedAt), {
    addSuffix: true,
  });

  return (
    <>
      <div
        className={cn(
          "group bg-card/40 relative rounded-xl border border-primary/20 p-5 overflow-hidden shadow shadow-primary/10 transition-all duration-300 hover:shadow-md bg-gradient-to-br from-primary/5 via-transparent to-primary/5",
          isNews && "border-border/80 dark:border-primary-800/50",
          className
        )}
      >
        {/* Platform and Actions Header */}
        <div
          className={cn("absolute top-4 right-4 flex items-center gap-3 z-10")}
        >
          {/* Repurpose Button - moved to header */}
          {!hideRepurposeButton && (
            <button
              onClick={() => setRepurposeDialogOpen(true)}
              className="group/btn p-2 rounded-full bg-gradient-to-br from-primary/80 to-primary text-white shadow-sm transition-all duration-300 flex items-center justify-center gap-0.5 text-xs font-medium"
              aria-label="Repurpose content"
            >
              <Wand2 className="h-3.5 w-3.5 group-hover/btn:scale-110 transition-transform" />
            </button>
          )}
        </div>

        {/* Author section */}
        {author && (
          <div className="flex items-center gap-3 mb-4">
            <Avatar className="border-2 border-background shadow-sm">
              <AvatarImage src={author.profileImage || ""} />
              <AvatarFallback>
                {author.name?.[0] || author.username?.[0] || "U"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="font-medium leading-none flex items-center gap-1">
                {author.name}
                {/* <span className="text-xs text-muted-foreground font-normal">@{author.username}</span> */}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {formattedDate}
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div
          className={cn(
            hasMedia ? "mb-3" : "",
            "prose max-w-none dark:prose-invert text-sm"
          )}
        >
          <div
            className={cn(
              isNews && "font-medium text-sm",
              "whitespace-pre-wrap"
            )}
          >
            {displayParagraphs.map((paragraph: string, index: number) =>
              paragraph.trim() ? (
                <p key={index}>{paragraph}</p>
              ) : (
                <br key={index} />
              )
            )}
          </div>
          {hasLongContent && !expanded && (
            <div className="-mt-1">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-primary/70 hover:text-primary h-5 px-2 -ml-2 rounded-none flex items-center gap-1 font-medium"
                onClick={() => setExpanded(true)}
              >
                Show more
                <ChevronDown className="h-3 w-3" />
              </Button>
            </div>
          )}

          {expanded && (
            <div className="-mt-1">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-primary/70 hover:text-primary h-5 px-2 -ml-2 rounded-none flex items-center gap-1 font-medium"
                onClick={() => setExpanded(false)}
              >
                Show less
                <ChevronDown className="h-3 w-3 rotate-180" />
              </Button>
            </div>
          )}
        </div>

        {/* Media content */}
        {hasMedia && (
          <div className="mb-3">
            {images && images.length > 0 ? (
              <div
                className={cn(
                  "grid gap-1 rounded-lg overflow-hidden",
                  images.length === 1 && "grid-cols-1 aspect-video",
                  images.length === 2 && "grid-cols-2 aspect-[16/9]",
                  images.length === 3 &&
                    "grid-cols-2 grid-rows-2 aspect-square",
                  images.length >= 4 && "grid-cols-2 grid-rows-2 aspect-square"
                )}
              >
                {images.slice(0, 4).map((image: string, index: number) => (
                  <div
                    key={index}
                    className={cn(
                      "overflow-hidden relative bg-muted",
                      images.length === 3 &&
                        index === 0 &&
                        "col-span-2 row-span-1"
                    )}
                  >
                    <img
                      src={image || ""}
                      alt={`Post media ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    {images.length > 4 && index === 3 && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white font-medium">
                        <span>+{images.length - 4} more</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : null}
          </div>
        )}

        {/* Metrics and action */}
        <div className="mt-4 pt-4 flex flex-col gap-3">
          {/* Engagement metrics */}
          <div className="flex space-x-6 justify-between">
            {/* Reactions - LinkedIn style */}
            {isLinkedIn ? (
              <HoverCard>
                <HoverCardTrigger asChild>
                  <div className="flex items-center text-muted-foreground group/like cursor-pointer">
                    <div className="flex -space-x-1.5">
                      {topReactions.length > 0 ? (
                        topReactions.map(([type], index) => (
                          <div
                            key={type}
                            className={cn(
                              "flex items-center justify-center rounded-full transition-all duration-200 ease-in-out",
                              "group-hover/like:translate-y-[-2px] group-hover/like:shadow-md",
                              index === 0 && "z-30",
                              index === 1 && "z-20 translate-x-1",
                              index === 2 && "z-10 translate-x-2"
                            )}
                            style={{
                              transitionDelay: `${index * 50}ms`,
                            }}
                          >
                            {reactionIcons[type as keyof typeof reactionIcons]}
                          </div>
                        ))
                      ) : (
                        <div className="p-1 rounded-full bg-white border border-border shadow-sm">
                          <ThumbsUp className="h-3.5 w-3.5 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <span className="text-xs ml-3 font-semibold text-foreground group-hover/like:text-primary transition">
                      {formatNumber(totalLikes)}
                    </span>
                  </div>
                </HoverCardTrigger>
                <HoverCardContent
                  className="p-3 w-52 shadow-lg rounded-lg"
                  align="start"
                >
                  <div className="text-sm font-medium mb-3">All reactions</div>
                  <div className="space-y-2.5">
                    {Object.entries(reactions || {}).map(([type, count]) =>
                      count ? (
                        <div
                          key={type}
                          className="flex items-center justify-between rounded-md p-1.5 hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex items-center gap-2">
                            {reactionIcons[type as keyof typeof reactionIcons]}
                            <span className="text-xs capitalize font-medium">
                              {type}
                            </span>
                          </div>
                          <span className="text-xs text-muted-foreground font-medium">
                            {formatNumber(count)}
                          </span>
                        </div>
                      ) : null
                    )}
                  </div>
                </HoverCardContent>
              </HoverCard>
            ) : (
              <div className="flex items-center cursor-pointer">
                <div className="p-1 rounded-full text-primary">
                  <Heart className="h-4 w-4 text-red-400 fill-red-400" />
                </div>
                <span className="text-xs font-semibold ml-1">
                  {formatNumber(totalLikes)}
                </span>
              </div>
            )}

            <div className="flex items-center gap-3">
              <div className="flex items-center cursor-pointer">
                <div className="p-1 rounded-full bg-primary/5 text-primary">
                  <MessageCircle className="h-3.5 w-3.5" />
                </div>
                <span className="text-xs font-semibold ml-1">
                  {formatNumber(totalComments)}
                </span>
              </div>

              <div className="flex items-center cursor-pointer">
                <div className="p-1 rounded-full bg-primary/5 text-primary">
                  <Share2 className="h-3.5 w-3.5" />
                </div>
                <span className="text-xs font-semibold ml-1">
                  {formatNumber(totalShares)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
