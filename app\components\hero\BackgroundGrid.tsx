import GridCell from "./GridCell";

interface BackgroundGridProps {
  gridOpacity?: number;
}

export const BackgroundGrid = ({ gridOpacity = 0.6 }: BackgroundGridProps) => {
  // Calculate reduced grid sizes for better performance
  const primaryGridSize = 8 * 16; // 8 cols x 16 rows instead of 12x24
  const secondaryGridSize = 6 * 12; // 6 cols x 12 rows

  return (
    <div
      className="fixed inset-0 z-0 pointer-events-none w-screen h-screen overflow-hidden"
      style={{
        opacity: gridOpacity,
        willChange: "opacity",
      }}
    >
      {/* Primary grid - reduced number of cells */}
      <div className="absolute -inset-[25%] grid grid-cols-8 grid-rows-16 scale-150">
        {Array.from({ length: primaryGridSize }).map((_, index) => (
          <GridCell key={`primary-${index}`} index={index} className="opacity-30" />
        ))}

      </div>

      {/* Secondary diagonal grid - reduced number of cells */}
      <div className="absolute -inset-[50%] rotate-[15deg] scale-2 opacity-30">
        <div className="absolute inset-0 grid grid-cols-6 grid-rows-12">
          {Array.from({ length: secondaryGridSize }).map((_, index) => (
            <GridCell key={`diagonal-${index}`} index={index + 50} className="opacity-30" />
          ))}
        </div>
      </div>

      {/* Gradient overlays - optimized with transform-gpu */}
      <div className="absolute inset-0 bg-linear-to-t from-background via-transparent to-transparent opacity-90 transform-gpu"></div>
      <div className="absolute inset-0 bg-linear-to-b from-background via-transparent to-transparent opacity-90 transform-gpu"></div>
      <div className="absolute inset-0 bg-linear-to-r from-background/70 via-transparent to-background/70 pointer-events-none transform-gpu"></div>
    </div>
  );
};

export default BackgroundGrid; 
