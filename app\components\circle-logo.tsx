import { cn } from "@/lib/utils";

export function CircleLogo({
  size = "medium",
  className,
}: {
  size?: "xs" | "small" | "medium" | "large";
  className?: string;
}) {
  const dimensions = {
    xs: { size: 28, viewBox: 100 },
    small: { size: 44, viewBox: 100 },
    medium: { size: 80, viewBox: 100 },
    large: { size: 128, viewBox: 100 },
  }[size];

  return (
    <div
      className={cn(
        "overflow-hidden relative rounded-full shadow-lg shadow-primary/40",
        size === "xs" && "h-7 w-7",
        size === "small" && "h-11 w-11",
        size === "medium" && "h-20 w-20",
        size === "large" && "h-32 w-32",
        className
      )}
    >
      <svg
        width={dimensions.size}
        height={dimensions.size}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#313236" />
            <stop offset="25%" stopColor="#252629">
              <animate
                attributeName="offset"
                values="25%;30%;25%"
                dur="8s"
                repeatCount="indefinite"
                calcMode="spline"
                keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
              />
            </stop>
            <stop offset="50%" stopColor="#313236">
              <animate
                attributeName="offset"
                values="50%;55%;50%"
                dur="8s"
                repeatCount="indefinite"
                calcMode="spline"
                keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
              />
            </stop>
            <stop offset="75%" stopColor="#252629">
              <animate
                attributeName="offset"
                values="75%;70%;75%"
                dur="8s"
                repeatCount="indefinite"
                calcMode="spline"
                keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
              />
            </stop>
            <stop offset="100%" stopColor="#313236" />
          </linearGradient>

          <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#fed7aa" stopOpacity="0.6">
              <animate
                attributeName="stop-opacity"
                values="0.6;0.4;0.6"
                dur="4s"
                repeatCount="indefinite"
                calcMode="spline"
                keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
              />
            </stop>
            <stop offset="100%" stopColor="#fdba74" stopOpacity="0" />
          </radialGradient>

          <linearGradient id="strokeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#fed7aa" stopOpacity="1">
              <animate
                attributeName="stop-color"
                values="#fed7aa; #fdba74; #fb923c"
                dur="4s"
                repeatCount="indefinite"
              />
            </stop>
            <stop offset="50%" stopColor="#fdba74" stopOpacity="1">
              <animate
                attributeName="stop-color"
                values="#fdba74; #fb923c; #fdba74"
                dur="4s"
                repeatCount="indefinite"
              />
            </stop>
            <stop offset="100%" stopColor="#fed7aa" stopOpacity="1">
              <animate
                attributeName="stop-color"
                values="#fed7aa; #fdba74; #fb923c"
                dur="4s"
                repeatCount="indefinite"
              />
            </stop>
          </linearGradient>

          <radialGradient id="particleGradient1" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#fbbf24" stopOpacity="1" />
            <stop offset="100%" stopColor="#f59e0b" stopOpacity="0.8" />
          </radialGradient>

          <radialGradient id="particleGradient2" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#fb923c" stopOpacity="1" />
            <stop offset="100%" stopColor="#f97316" stopOpacity="0.6" />
          </radialGradient>

          <mask id="circleMask">
            <circle cx="50" cy="50" r="50" fill="white" />
          </mask>

          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="1" />
            <feOffset dx="0" dy="0" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.15" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>

          <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur" />
            <feFlood
              floodColor="#fdba74"
              floodOpacity="0.1"
              result="glowColor"
            />
            <feComposite
              in="glowColor"
              in2="coloredBlur"
              operator="in"
              result="softGlow"
            />
            <feMerge>
              <feMergeNode in="softGlow" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Background with animated shadow */}
        <g mask="url(#circleMask)">
          <g filter="url(#shadow)">
            <g>
              <rect
                width="100"
                height="100"
                x="0"
                y="0"
                fill="url(#bgGradient)"
              />
            </g>
            <rect
              width="100"
              height="100"
              x="0"
              y="0"
              fill="url(#glowGradient)"
              opacity="0.98"
            />

            {/* Infinity Symbol Design */}
            <g transform="translate(50, 50) rotate(30) translate(-50, -50)">
              {/* Base infinity shape with glow */}
              <path
                d="M30,50 C30,42 34,35 40,35 C46,35 49,42 50,50 C51,42 54,35 60,35 C66,35 70,42 70,50 C70,58 66,65 60,65 C54,65 51,58 50,50 C49,58 46,65 40,65 C34,65 30,58 30,50"
                stroke="#fed7aa"
                strokeWidth="10"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="none"
                opacity="0.2"
                filter="none"
              >
                <animate
                  attributeName="stroke"
                  values="#fed7aa;#fdba74;#fb923c"
                  dur="4s"
                  repeatCount="indefinite"
                  calcMode="spline"
                  keySplines="0.4 0 0.2 1; 0.4 0 0.2 1"
                />
              </path>

              {/* Animated infinity - Main Stroke */}
              <path
                d="M30,50 C30,42 34,35 40,35 C46,35 49,42 50,50 C51,42 54,35 60,35 C66,35 70,42 70,50 C70,58 66,65 60,65 C54,65 51,58 50,50 C49,58 46,65 40,65 C34,65 30,58 30,50"
                stroke="url(#strokeGradient)"
                strokeWidth="9"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="none"
                opacity="1"
                filter="url(#glow)"
              >
                <animate
                  attributeName="stroke-dasharray"
                  values="0 220;220 220"
                  dur="4s"
                  repeatCount="indefinite"
                  calcMode="spline"
                  keySplines="0.25 0.1 0.25 1"
                />
                <animate
                  attributeName="stroke-dashoffset"
                  values="220;0"
                  dur="4s"
                  repeatCount="indefinite"
                  calcMode="spline"
                  keySplines="0.25 0.1 0.25 1"
                />
              </path>

              {/* Floating particles */}
              <g filter="none">
                <circle r="1.5" fill="#fed7aa" opacity="0.8">
                  <animateMotion
                    path="M30,50 C30,42 34,35 40,35 C46,35 49,42 50,50 C51,42 54,35 60,35 C66,35 70,42 70,50 C70,58 66,65 60,65 C54,65 51,58 50,50 C49,58 46,65 40,65 C34,65 30,58 30,50"
                    dur="4s"
                    repeatCount="indefinite"
                    calcMode="spline"
                    keyPoints="0;1"
                    keyTimes="0;1"
                    keySplines="0.25 0.1 0.25 1"
                  />
                  <animate
                    attributeName="fill"
                    values="#fed7aa;#fdba74;#fb923c"
                    dur="4s"
                    repeatCount="indefinite"
                  />
                </circle>
              </g>

              {/* Enhanced Dust Particle System */}
              <g filter="url(#glow)">
                {/* Yellow particles (formerly blue) */}
                {[...Array(12)].map((_, i) => (
                  <circle
                    key={`yellow-${i}`}
                    r={1 + Math.random() * 1.5}
                    fill="url(#particleGradient1)"
                    opacity={0.4 + Math.random() * 0.4}
                  >
                    <animateMotion
                      path="M30,50 C30,42 34,35 40,35 C46,35 49,42 50,50 C51,42 54,35 60,35 C66,35 70,42 70,50 C70,58 66,65 60,65 C54,65 51,58 50,50 C49,58 46,65 40,65 C34,65 30,58 30,50"
                      dur={`${3 + Math.random() * 2}s`}
                      begin={`-${Math.random() * 3}s`}
                      repeatCount="indefinite"
                    />
                    <animate
                      attributeName="opacity"
                      values="0;0.8;0.8;0.4;0"
                      keyTimes="0;0.2;0.6;0.8;1"
                      dur={`${3 + Math.random() * 2}s`}
                      repeatCount="indefinite"
                    />
                  </circle>
                ))}

                {/* Orange particles */}
                {[...Array(12)].map((_, i) => (
                  <circle
                    key={`orange-${i}`}
                    r={1 + Math.random() * 1.5}
                    fill="url(#particleGradient2)"
                    opacity={0.4 + Math.random() * 0.4}
                  >
                    <animateMotion
                      path="M30,50 C30,42 34,35 40,35 C46,35 49,42 50,50 C51,42 54,35 60,35 C66,35 70,42 70,50 C70,58 66,65 60,65 C54,65 51,58 50,50 C49,58 46,65 40,65 C34,65 30,58 30,50"
                      dur={`${3 + Math.random() * 2}s`}
                      begin={`-${Math.random() * 3}s`}
                      repeatCount="indefinite"
                    />
                    <animate
                      attributeName="opacity"
                      values="0;0.8;0.8;0.4;0"
                      keyTimes="0;0.2;0.6;0.8;1"
                      dur={`${3 + Math.random() * 2}s`}
                      repeatCount="indefinite"
                    />
                  </circle>
                ))}

                {/* Outer scattered particles */}
                {[...Array(15)].map((_, i) => (
                  <circle
                    key={`scatter-${i}`}
                    r={0.8 + Math.random()}
                    fill={i % 2 ? "url(#particleGradient1)" : "url(#particleGradient2)"}
                    opacity={0.3 + Math.random() * 0.3}
                  >
                    <animateMotion
                      path={`M${40 + Math.random() * 20},${40 + Math.random() * 20} C${30 + Math.random() * 40},${30 + Math.random() * 40} ${30 + Math.random() * 40},${30 + Math.random() * 40} ${40 + Math.random() * 20},${40 + Math.random() * 20}`}
                      dur={`${4 + Math.random() * 3}s`}
                      begin={`-${Math.random() * 4}s`}
                      repeatCount="indefinite"
                    />
                    <animate
                      attributeName="opacity"
                      values="0;0.6;0.6;0.3;0"
                      keyTimes="0;0.2;0.6;0.8;1"
                      dur={`${4 + Math.random() * 3}s`}
                      repeatCount="indefinite"
                    />
                  </circle>
                ))}
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
  );
}
