import { shortify } from "@/content/lib";
import Image from "next/image";
import Link from "next/link";

export default function BlogCard({
  slug,
  coverImage,
  title,
  description,
}: {
  slug: string;
  coverImage: string;
  title: string;
  description: string;
}) {
  return (
    <Link
      key={slug}
      href={`/blog/${slug}`}
      className="rounded-2xl group border relative"
    >
      <div className="relative h-60 overflow-hidden rounded-t-2xl">
        <Image
          src={coverImage}
          alt={""}
          style={{ objectFit: "cover" }}
          fill={true}
          className=" group-hover:scale-110 transition-all duration-500"
        />
      </div>
      <div className="px-4 py-6 space-y-2">
        <h3 className="text-base font-medium line-clamp-1 ">{title}</h3>
        <p className="text-base text-gray-400 line-clamp-2">
          {shortify(description, 100)}
        </p>
      </div>
    </Link>
  );
}
