"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import TextGradient from "@/components/ui/text-gradient";
import { motion, AnimatePresence } from "framer-motion";
import { Mi<PERSON>, Phone, PhoneCall } from "lucide-react";
import { cn } from "@/lib/utils";
import { Ripple } from "@/components/magicui/ripple";
import React from "react";

interface FormData {
  name: string;
  phone: string;
  email: string;
  agentType: string;
}

interface FormErrors {
  name?: string;
  phone?: string;
  email?: string;
}

type CallState = "form" | "calling" | "connected" | "ended";

const MicrophoneAnimation = () => {
  return (
    <div className="flex items-center justify-center space-x-1">
      {[...Array(7)].map((_, i) => (
        <motion.div
          key={i}
          className="w-1 bg-primary rounded-full"
          animate={{
            height: [4, 16, 4],
            opacity: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: i * 0.1,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

const RippleBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute inset-0 [mask-image:radial-gradient(circle_at_center,white,transparent_80%)]">
        <Ripple
          mainCircleSize={400}
          mainCircleOpacity={0.25}
          numCircles={8}
          className="opacity-70 [--ripple-bg:hsl(var(--primary)_/_0.15)]"
          style={
            {
              "--foreground": "var(--primary)",
            } as React.CSSProperties
          }
        />
      </div>
    </div>
  );
};

const agents = [
  { displayName: "Patient Screening", agentId: "patient-screening" },
  {
    displayName: "Real Estate Qualification",
    agentId: "real-estate-qualification",
  },
  {
    displayName: "Medical Center Receptionist",
    agentId: "medical-center-receptionist",
  },
  { displayName: "Dentist Reschedule", agentId: "dentist-reschedule" },
];

export default function NoahPage() {
  const phoneNumber = {
    display: "+****************",
    value: "+16066127449",
  };

  const [formData, setFormData] = useState<FormData>({
    name: "",
    phone: "",
    email: "",
    agentType: "patient-screening",
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [callState, setCallState] = useState<CallState>("form");
  const [isLoading, setIsLoading] = useState(false);

  const validateName = (name: string): string | undefined => {
    if (!name.trim()) {
      return "Name is required";
    }
    if (name.trim().length < 2) {
      return "Name must be at least 2 characters long";
    }
    if (!/^[a-zA-Z\s'-]+$/.test(name.trim())) {
      return "Name can only contain letters, spaces, hyphens, and apostrophes";
    }
    return undefined;
  };

  const validateEmail = (email: string): string | undefined => {
    if (!email.trim()) {
      return "Email is required";
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return "Please enter a valid email address";
    }
    return undefined;
  };

  const validatePhone = (phone: string): string | undefined => {
    if (!phone.trim()) {
      return "Phone number is required";
    }

    // Check for country code format
    if (!phone.startsWith("+")) {
      return "Phone number must start with country code (e.g., +1 for US)";
    }

    // Remove all non-digit characters for validation
    const cleanPhone = phone.replace(/\D/g, "");

    // Check minimum length (country code + local number)
    if (cleanPhone.length < 11) {
      // At least 1 digit country code + 10 digits number
      return "Phone number must include country code and at least 10 digits";
    }

    if (cleanPhone.length > 15) {
      return "Phone number cannot exceed 15 digits";
    }

    // Enhanced phone regex that requires country code
    const phoneRegex = /^\+\d{1,4}[\d\s\-\(\)]{10,}$/;
    if (!phoneRegex.test(phone.trim())) {
      return "Please enter a valid phone number with country code";
    }

    return undefined;
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    errors.name = validateName(formData.name);
    errors.email = validateEmail(formData.email);
    errors.phone = validatePhone(formData.phone);

    setFormErrors(errors);

    // Return true if no errors
    return !Object.values(errors).some((error) => error !== undefined);
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[field as keyof FormErrors]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  async function sendDataToWebhook() {
    await fetch("https://n8n.tryinloop.com/webhook/save-lead", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    });
  }

  const handleCallRequest = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setCallState("calling");

    sendDataToWebhook();

    // Simulate API call to initiate the call
    setTimeout(() => {
      setIsLoading(false);
      setCallState("connected");

      // Show connected state for 3 seconds, then show direct call option
      setTimeout(() => {
        setCallState("ended");
      }, 3000);
    }, 2000);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      phone: "",
      email: "",
      agentType: "patient-screening",
    });
    setFormErrors({});
    setCallState("form");
  };

  // Helper function to get display name from agent ID
  const getAgentDisplayName = (agentId: string) => {
    const agent = agents.find((a) => a.agentId === agentId);
    return agent ? agent.displayName : agentId;
  };

  return (
    <main className="relative overflow-hidden min-h-screen bg-background">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none"></div>
      <div className="sm:block hidden absolute top-20 left-[30%] opacity-[0.08] transform scale-150 gradient-blob"></div>

      {/* Main Content */}
      <div className="relative pt-32 pb-20">
        <div className="max-w-6xl mx-auto px-4">
          {/* Header Section */}
          <div className="text-center mb-16">
            <h1 className="mb-6 text-4xl sm:text-6xl leading-tight font-medium text-foreground">
              Meet <TextGradient txt="Noah" /> - Your AI Receptionist
            </h1>
            <p className="text-lg sm:text-xl font-normal text-muted-foreground max-w-xl mx-auto leading-relaxed">
              Noah handles your queries, appointments, rescheduling and more.
            </p>
          </div>

          {/* Main Form Card with CTAWilliam styling */}
          <div className="relative">
            <div className="relative p-8 sm:p-12 rounded-3xl shadow shadow-primary/20 overflow-hidden bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-md border border-primary/20">
              {/* Ripple effect as dynamic background */}
              <RippleBackground />
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>
              <div className="relative z-10">
                <AnimatePresence mode="wait">
                  {callState === "form" && (
                    <motion.div
                      key="form"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="grid lg:grid-cols-2 gap-8">
                        {/* Left Column - Form */}
                        <div className="lg:pr-8">
                          <h2 className="text-2xl font-semibold mb-8 text-foreground text-center lg:text-left">
                            Get a call from Noah
                          </h2>

                          {/* Form Fields */}
                          <div className="space-y-6 max-w-md mx-auto lg:mx-0">
                            <div>
                              <label className="block text-sm font-medium mb-2 text-muted-foreground text-left">
                                Name
                              </label>
                              <input
                                type="text"
                                value={formData.name}
                                onChange={(e) =>
                                  handleInputChange("name", e.target.value)
                                }
                                className={cn(
                                  "w-full px-4 py-3 rounded-lg border bg-background/80 backdrop-blur-sm text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:border-transparent transition-all",
                                  formErrors.name
                                    ? "border-red-500 focus:ring-red-500"
                                    : "border-border focus:ring-primary"
                                )}
                                placeholder="Your name"
                              />
                              {formErrors.name && (
                                <p className="mt-1 text-sm text-red-500 text-left">
                                  {formErrors.name}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-2 text-muted-foreground text-left">
                                Phone
                              </label>
                              <input
                                type="tel"
                                value={formData.phone}
                                onChange={(e) =>
                                  handleInputChange("phone", e.target.value)
                                }
                                className={cn(
                                  "w-full px-4 py-3 rounded-lg border bg-background/80 backdrop-blur-sm text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:border-transparent transition-all",
                                  formErrors.phone
                                    ? "border-red-500 focus:ring-red-500"
                                    : "border-border focus:ring-primary"
                                )}
                                placeholder="Your phone number"
                              />
                              {formErrors.phone && (
                                <p className="mt-1 text-sm text-red-500 text-left">
                                  {formErrors.phone}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-2 text-muted-foreground text-left">
                                Email
                              </label>
                              <input
                                type="email"
                                value={formData.email}
                                onChange={(e) =>
                                  handleInputChange("email", e.target.value)
                                }
                                className={cn(
                                  "w-full px-4 py-3 rounded-lg border bg-background/80 backdrop-blur-sm text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:border-transparent transition-all",
                                  formErrors.email
                                    ? "border-red-500 focus:ring-red-500"
                                    : "border-border focus:ring-primary"
                                )}
                                placeholder="<EMAIL>"
                              />
                              {formErrors.email && (
                                <p className="mt-1 text-sm text-red-500 text-left">
                                  {formErrors.email}
                                </p>
                              )}
                            </div>
                          </div>

                          {/* Call Button */}
                          <div className="my-8">
                            <Button
                              onClick={handleCallRequest}
                              disabled={isLoading}
                              className={cn(
                                "w-full max-w-md py-4 text-lg font-medium rounded-lg mx-auto lg:mx-0",
                                "flex items-center justify-center text-center",
                                "bg-primary text-primary-foreground hover:bg-primary/90",
                                "transform transition-all duration-200 hover:scale-[1.02]",
                                isLoading && "opacity-70 cursor-not-allowed"
                              )}
                            >
                              {isLoading ? "Initiating Call..." : "Call me Now"}
                            </Button>
                          </div>
                        </div>

                        {/* Right Column - Agent Selection */}
                        <div className="lg:pl-8 lg:border-l border-border/50">
                          <h3 className="hidden md:block text-lg font-medium mb-6 text-foreground text-center lg:text-left">
                            Select Agent Type
                          </h3>
                          <div className="grid grid-cols-2 gap-4 max-w-md mx-auto lg:mx-0 lg:max-w-none">
                            {agents.map((agent, index) => {
                              const isSelected =
                                formData.agentType === agent.agentId;
                              return (
                                <button
                                  key={index}
                                  type="button"
                                  onClick={() =>
                                    handleInputChange(
                                      "agentType",
                                      agent.agentId
                                    )
                                  }
                                  className={cn(
                                    "p-6 text-sm font-medium rounded-xl border transition-all duration-200",
                                    "hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                                    "min-h-[80px] flex items-center justify-center text-center",
                                    "backdrop-blur-sm",
                                    isSelected
                                      ? "bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/25"
                                      : "bg-background/60 text-muted-foreground border-border/50 hover:bg-background/80 hover:text-foreground hover:border-primary/50 hover:shadow-md"
                                  )}
                                >
                                  {agent.displayName}
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {callState === "calling" && (
                    <motion.div
                      key="calling"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.5 }}
                      className="text-center py-16"
                    >
                      <div className="mb-8">
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "linear",
                          }}
                          className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary/20 flex items-center justify-center"
                        >
                          <PhoneCall className="w-8 h-8 text-primary" />
                        </motion.div>
                      </div>
                      <h3 className="text-xl font-medium text-foreground mb-2">
                        Connecting you to Noah (
                        {getAgentDisplayName(formData.agentType)})...
                      </h3>
                      <p className="text-muted-foreground">
                        Please wait while we establish the connection
                      </p>
                    </motion.div>
                  )}

                  {callState === "connected" && (
                    <motion.div
                      key="connected"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.5 }}
                      className="text-center py-16"
                    >
                      <div className="mb-8">
                        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-primary/20 flex items-center justify-center">
                          <Mic className="w-10 h-10 text-primary" />
                        </div>
                        <MicrophoneAnimation />
                      </div>
                      <h3 className="text-xl font-medium text-foreground mb-2">
                        Connected to Noah (
                        {getAgentDisplayName(formData.agentType)})
                      </h3>
                      <p className="text-muted-foreground">
                        Your call is now in progress
                      </p>
                    </motion.div>
                  )}

                  {callState === "ended" && (
                    <motion.div
                      key="ended"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="text-center py-8"
                    >
                      <div className="mb-6">
                        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
                          <Phone className="w-8 h-8 text-green-500" />
                        </div>
                      </div>
                      <h3 className="text-xl font-medium text-foreground mb-2">
                        Great! Call completed successfully
                      </h3>
                      <p className="text-muted-foreground mb-6">
                        Want to try again or call directly?
                      </p>

                      <div className="space-y-4 max-w-md mx-auto">
                        <Button
                          onClick={resetForm}
                          variant="outline"
                          className="w-full backdrop-blur-sm"
                        >
                          Try Again
                        </Button>

                        <div className="text-center">
                          <p className="text-sm text-muted-foreground mb-3">
                            Or call Noah directly
                          </p>
                          <a
                            href={`tel:${phoneNumber.value}`}
                            className="inline-flex items-center px-6 py-3 rounded-xl border border-border/50 bg-background/60 backdrop-blur-sm hover:bg-background/80 text-foreground font-medium transition-all duration-200 hover:shadow-md hover:scale-105"
                          >
                            📞 {phoneNumber.display}
                          </a>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Demo Call Section - Similar to attached image */}
              <div className="relative z-10 text-center pt-16 pb-4 mt-8 border-t border-border/30">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="space-y-8"
                >
                  {/* Microphone with Sound Waves */}

                  {/* Call Text */}
                  <div className="space-y-2">
                    <p className="text-lg text-muted-foreground">
                      Call{" "}
                      <a
                        href={`tel:${phoneNumber.value}`}
                        className="text-primary font-semibold hover:text-primary/80 transition-colors"
                      >
                        {phoneNumber.display}
                      </a>{" "}
                      to try us out.
                    </p>
                  </div>

                  {/* Demo Button */}
                  <div>
                    <Button
                      asChild
                      size="lg"
                      className="px-8 py-4 text-lg font-medium rounded-full bg-primary hover:bg-primary/90 text-primary-foreground transform transition-all duration-200 hover:scale-105"
                    >
                      <a
                        href={`tel:${phoneNumber.value}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Call Noah Now
                      </a>
                    </Button>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
