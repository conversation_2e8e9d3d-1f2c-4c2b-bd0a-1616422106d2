import { memo } from "react";

const sampleLogos = [
  {
    src: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/google.svg",
    alt: "Google",
  },
  {
    src: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/apple.svg",
    alt: "Apple",
  },
  {
    src: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/microsoft.svg",
    alt: "Microsoft",
  },
  {
    src: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/amazon.svg",
    alt: "Amazon",
  },
  {
    src: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/meta.svg",
    alt: "Meta",
  },
];

// Memoize the logo component for better performance
const LogoItem = memo(({ logo }: { logo: (typeof sampleLogos)[0] }) => (
  <div className="flex items-center justify-center">
    <div className="h-10 w-full flex items-center justify-center rounded-lg border border-primary/10 bg-primary/[0.02] hover:border-primary/20 hover:bg-primary/[0.03] transition-colors duration-150 cursor-pointer group">
      <div className="relative w-16 h-6">
        <img
          src={logo.src}
          alt={logo.alt}
          width={96}
          height={32}
          loading="lazy"
          decoding="async"
          className="w-full h-full object-contain opacity-50 group-hover:opacity-70 transform-gpu"
          style={{ filter: "invert(0.4)" }}
        />
      </div>
    </div>
  </div>
));

LogoItem.displayName = "LogoItem";

export const TrustedBySection = ({
  heading = "Trusted by Founders, Professionals & Sales Leaders",
}: {
  heading?: string;
}) => {
  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-8 pb-16 relative mt-4">
      <div className="text-center">
        <div className="inline-flex flex-col items-center">
          <span className="text-lg sm:text-xl md:text-2xl text-muted-foreground font-light mb-6">
            {heading}
          </span>

          <div className="grid grid-cols-3 md:grid-cols-5 gap-x-12 gap-y-8 will-change-transform">
            {sampleLogos.map((logo, index) => (
              <LogoItem key={index} logo={logo} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(TrustedBySection);
