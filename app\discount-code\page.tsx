import HeroDescription from "../components/common/HeroDescription";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "TryInloop Discount Code | How to Redeem Your Special Offer",
  description: "How to redeem TryInloop Discount Code?",
  keywords: "tryinloop discount code repemption, how to redeem tryinloop code",
  openGraph: {
    title: "TryInloop Discount Code",
    description: "How to redeem TryInloop Discount Code?",
    url: "https://tryinloop.com/discount-code",
    images: [
      {
        url: "https://tryinloop.com/meta-images/discount-code.png",
        width: 800,
        height: 400,
      },
    ],
  },
};

export default function DiscountCode() {
  return (
    <div className="py-10 mt-26 relative">
      <div className="max-w-5xl mx-auto">
        <HeroDescription
          headingLine1="TryInloop"
          headingLine2="Discount Code"
          subHeading="How to redeem TryInloop Discount Code?"
          paragraph={
            <>
              <p className="mb-4">
                TryInloop Discount Codes are distributed exclusively via our
                partner network.
              </p>
            </>
          }
        />

        <div className="space-y-12">
          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              How to Redeem?
            </h2>
            <p className="mb-4 text-muted-foreground/90 leading-relaxed">
              To access the benefits of Discount Codes, please follow the below
              steps:
            </p>
            <ol className="list-decimal pl-6 mb-6 space-y-3 text-muted-foreground/90">
              <li className="leading-relaxed">
                Login to the account at{" "}
                <a
                  href="https://tryinloop.com"
                  className="text-blue-600 hover:underline"
                >
                  Tryinloop.com
                </a>
              </li>
              <li className="leading-relaxed">
                Go Credits &gt; Plans &gt; Choose Plan &gt; Payment Page
              </li>
              <li className="leading-relaxed">Apply Code</li>
              <li className="leading-relaxed">Pay</li>
            </ol>
          </div>

          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              Terms and Conditions
            </h2>
            <p className="mb-4 text-muted-foreground/90 leading-relaxed">
              General terms and condition of TryInloop Discount Codes:
            </p>
            <ul className="list-disc pl-6 mb-6 space-y-3 text-muted-foreground/90">
              <li className="leading-relaxed">
                Coupons are valid for a limited time only. TryInloop reserves
                the right to modify or cancel coupons at any time.
              </li>
              <li className="leading-relaxed">
                The coupon offer will not be valid until it is applied to the
                qualifying product.
              </li>
              <li className="leading-relaxed">
                The promotion is limited to one coupon per customer.
              </li>
              <li className="leading-relaxed">
                Promotion may not be combined with other promotions.
              </li>
              <li className="leading-relaxed">Void where prohibited.</li>
              <li className="leading-relaxed">
                TryInloop has no obligation for payment of any tax in
                conjunction with the distribution or use of any coupon.
              </li>
              <li className="leading-relaxed">
                Consumer is required to pay any applicable sales tax related to
                the use of the coupon.
              </li>
              <li className="leading-relaxed">
                Coupons are void if restricted or prohibited by law.
              </li>
            </ul>
          </div>

          <div className="bg-linear-to-br from-card/30 via-card/20 to-card/5 backdrop-blur-xs rounded-xl p-4 border border-border/5">
            <h2 className="text-3xl font-bold text-primary mt-0 mb-6 tracking-tight">
              Questions?
            </h2>
            <div className="space-y-4 text-muted-foreground">
              <p className="mb-4 text-muted-foreground/90 leading-relaxed">
                If you have any questions about redeeming your discount code,
                please contact us at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
