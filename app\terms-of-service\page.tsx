import BlobAnimation from "../components/common/BlobAnimation";
import { Metadata } from "next";
import TermsOfServiceBody from "../components/terms-of-service-body";

export const metadata: Metadata = {
  title: "Inloop Terms and Conditions | Service Usage Agreement",
  description:
    "Please read these terms and conditions carefully before using Our Service.",
  keywords: "tryinloop terms of service",
  openGraph: {
    title: "Terms and Conditions",
    description:
      "Please read these terms and conditions carefully before using Our Service.",
    url: "https://tryinloop.com/terms-of-service",
    images: [
      {
        url: "https://tryinloop.com/meta-images/terms-of-service.png",
        width: 800,
        height: 400,
      },
    ],
  },
};

export default function TermsOfService() {
  return (
    <div className="mt-32">
      <BlobAnimation />
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none" />
      <TermsOfServiceBody />
    </div>
  );
}
