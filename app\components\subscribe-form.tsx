"use client";

import { useState } from "react";
import { AtSign } from "lucide-react";

import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

const URL =
  "https://xpanse.us21.list-manage.com/subscribe/post-json?u=dae41ff3c152fa1373a9c9841&id=c8318dba2c";

export default function SubscribeForm() {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState<"sending" | "error" | "success">();
  const handleSubmit = () => {
    if (!email) return;
    setStatus("sending");
    fetch(`${URL}&EMAIL=${email}`, {
      mode: "no-cors",
    })
      .then((result: any) => setStatus("success"))
      .catch((error: any) => setStatus("error"));
  };

  return (
    <div className="w-full max-w-xl">
      <label
        htmlFor="email"
        className="hidden mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
      >
        Email
      </label>
      <div className="relative">
        <div className="z-20 absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none ">
          <AtSign />
        </div>

        <input
          disabled={status === "sending"}
          type="email"
          id="email"
          className="block w-full p-4 pl-10 text-sm border active:bg-white/10 focus:bg-white/10 rounded-full border-white/10 bg-white/10 backdrop-blur-3xl focus:ring-blue-500 focus:border-blue-500"
          placeholder="Your Email"
          required
          onChange={(e) => setEmail(e.target.value)}
        />
        <button
          disabled={status === "sending"}
          type="submit"
          className={cn(
            buttonVariants({ size: "lg" }),
            "absolute right-[7.5px] bottom-[7.5px] rounded-full h-10 px-6 font-semibold"
          )}
          onClick={handleSubmit}
        >
          {status === "sending" ? "Joining..." : "Join Waitlist"}
        </button>
      </div>

      {status === "error" && (
        <div className="text-red-500">
          There was an error subscribing. Try in sometime.
        </div>
      )}
      {status === "success" && (
        <div className="text-green-500">
          Thank you for joining the waitlist!
        </div>
      )}
    </div>
  );
}
