import { getBlogBySlug, getBlogs } from "@/content/lib";
import { ArrowLeft } from "lucide-react";
import { Metada<PERSON> } from "next";
import Image from "next/image";
import Link from "next/link";
import { ParsedUrlQuery } from "querystring";
import { FC, use } from "react";
import B<PERSON>Header from "./BlogHeader";

interface Params extends ParsedUrlQuery {
  slug: string;
}

type Props = {
  params: Params;
};

const getInitialBlog = async (slug: string) => {
  const blog = getBlogBySlug(slug);
  return blog;
};

const BlogDetail: FC<Props> = ({ params }) => {
  const blog = use(getInitialBlog(params.slug));

  return (
    <section className="relative mt-28 overflow-hidden">
      <div className="relative z-10 mx-auto">
        <div className="mx-auto mt-10 rounded max-w-prose lg:text-lg gap-y-10 gap-x-6 ">
          <BlogHeader blog={blog} />

          <article
            className="mx-auto mt-8 prose lg:prose-lg dark:prose-invert 
            prose-headings:text-foreground prose-headings:font-bold 
            prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl
            prose-p:text-muted-foreground prose-p:leading-relaxed
            prose-a:text-primary prose-a:no-underline hover:prose-a:underline
            prose-strong:text-foreground prose-strong:font-semibold
            prose-code:text-primary prose-code:bg-muted prose-code:rounded prose-code:px-1 prose-code:py-0.5
            prose-pre:bg-muted prose-pre:text-muted-foreground prose-pre:border prose-pre:border-border
            prose-blockquote:text-muted-foreground prose-blockquote:border-l-primary
            prose-img:rounded-lg prose-img:mx-auto prose-img:w-full
            prose-ul:text-muted-foreground prose-ol:text-muted-foreground
            prose-li:marker:text-primary"
          >
            <div dangerouslySetInnerHTML={{ __html: blog.content }} />
          </article>
          <div className="flex justify-between">
            <div className="flex items-start justify-start">
              <Link
                href="/blog"
                className="flex items-center justify-center px-4 py-2 text-base font-medium text-yellow-600 transition duration-300 ease-in-out bg-white bg-opacity-0 border-2 border-yellow-600 rounded-full hover:bg-yellow-600 hover:text-yellow-100 hover:shadow-lg"
              >
                <ArrowLeft size={24} className="mr-2" /> Back
              </Link>
            </div>
            <div className="flex flex-row justify-between mb-2">
              <div className="flex items-center">
                <div className="shrink-0">
                  <span className="sr-only">{blog.author}</span>
                  <div className="relative h-10 w-10 mb-0!">
                    <Image
                      priority
                      fill={true}
                      className="rounded-full"
                      src={blog.authorImage}
                      alt={blog.author}
                    />
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium mb-0!">
                    <a href="#" className="hover:underline">
                      {blog.author}
                    </a>
                  </p>
                  <div className="flex space-x-1 text-sm text-gray-400">
                    <time dateTime={blog.date}>{blog.date}</time>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const post = await getPost(params.slug);

  return {
    title: post.metaTitle,
    description: post.description,
    keywords:
      post.keywords ||
      `tryinloop blog, ${post.slug.replace(/-/g, " ")}, sales insights`,
    openGraph: {
      title: post.metaTitle,
      description: post.description,
      url: `https://tryinloop.com/blog/${post.slug}`,
      images: [
        {
          url: `https://tryinloop.com${post.coverImage}`,
          width: 800,
          height: 400,
        },
      ],
    },
  };
}

async function getPost(slug: string) {
  const blog = getBlogBySlug(slug);
  return blog;
}
export function generateStaticParams() {
  const blogs = getBlogs();

  return blogs.map((blog) => ({
    slug: blog.slug,
  }));
}

export default BlogDetail;
