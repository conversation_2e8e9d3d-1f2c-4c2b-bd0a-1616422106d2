import { Metadata } from "next";
import { FC, use } from "react";
import { getBlogs } from "@/content/lib";
import BlogCard from "../components/blog-card";
import HeroDescription from "../components/common/HeroDescription";

export const metadata: Metadata = {
  title:
    "Inloop Sales Blog | Expert Tips & Industry Insights for Sales Leaders",
  description: "Sales insights, tips, and tricks from the Inloop team.",
  keywords: "tryinloop blog for sales leaders, sdr and founders",
  openGraph: {
    title: "Inloop Sales Blog: Tips & Insights",
    description: "Sales insights, tips, and tricks from the Inloop team.",
    url: "https://tryinloop.com/blog",
    images: [
      {
        url: "https://tryinloop.com/meta-images/blog.png",
        width: 800,
        height: 400,
      },
    ],
  },
};
async function getInitialBlogs() {
  const blogs = getBlogs();
  return blogs;
}

const Page: FC = () => {
  const blogs = use(getInitialBlogs());

  return (
    <div className="relative mt-32">
      <HeroDescription
        headingLine1="Inloop"
        headingLine2="Insights Hub"
        paragraph="Sales insights, tips, and tricks from the Inloop team."
      />
      <section className="relative max-w-6xl m-auto">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 gap-x-6 ">
          {blogs.map((blog, key) => (
            <BlogCard
              title={blog.title}
              slug={blog.slug}
              description={blog.description}
              coverImage={blog.coverImage}
              key={key}
            />
          ))}
        </div>
      </section>
    </div>
  );
};

export default Page;
