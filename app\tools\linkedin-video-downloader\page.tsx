import { Metadata } from "next";
import LinkedinVideoDownloaderPage from "./components/LinkedinVideoDownloaderPage";

export const metadata: Metadata = {
  title: "Linkedin Video Downloader - Inloop for Viral Linkedin Content",
  description:
    "Download LinkedIn Videos from URLs with Inloop Free Tool. Copy and paste URL and download any LinkedIn Videos.",
  keywords: [
    "LinkedIn video downloader",
    "download LinkedIn videos",
    "LinkedIn post videos",
    "save LinkedIn videos",
    "video downloader tool",
    "viral linkedin content",
    "linkedin content creation",
  ],
  openGraph: {
    title: "Linkedin Video Downloader - Inloop for Viral Linkedin Content",
    description:
      "Download LinkedIn Videos from URLs with Inloop Free Tool. Copy and paste URL and download any LinkedIn Videos.",
    url: "https://tryinloop.com/tools/linkedin-video-downloader",
    siteName: "Inloop",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Linkedin Video Downloader - Inloop for Viral Linkedin Content",
    description:
      "Download LinkedIn Videos from URLs with Inloop Free Tool. Copy and paste URL and download any LinkedIn Videos.",
  },
  alternates: {
    canonical: "https://tryinloop.com/tools/linkedin-video-downloader",
  },
};

export default function Page() {
  return <LinkedinVideoDownloaderPage />;
}
