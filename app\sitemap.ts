import type { MetadataRoute } from "next";
import { getBlogs } from "@/content/lib";

export default function sitemap(): MetadataRoute.Sitemap {
  const blogs = getBlogs();
  const sitemap: MetadataRoute.Sitemap = [
    {
      url: "https://tryinloop.com",
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 1,
    },
    {
      url: "https://tryinloop.com/about",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: "https://tryinloop.com/blog",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
  ];

  const blogSitemap: MetadataRoute.Sitemap = blogs.map((blog) => ({
    url: `https://tryinloop.com/blog/${blog.slug}`,
    lastModified: new Date(),
    changeFrequency: "weekly",
    priority: 0.5,
  }));

  if (blogSitemap.length > 0) {
    sitemap.push(...blogSitemap);
  }

  return sitemap;
}
