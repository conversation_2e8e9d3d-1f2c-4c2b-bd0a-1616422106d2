"use client";

import { useState, useCallback, memo } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Play, XIcon } from "lucide-react";
import { cn } from "@/lib/utils";

type AnimationStyle =
  | "from-bottom"
  | "from-center"
  | "from-top"
  | "from-left"
  | "from-right"
  | "fade"
  | "top-in-bottom-out"
  | "left-in-right-out";

interface HeroVideoProps {
  animationStyle?: AnimationStyle;
  videoSrc: string;
  thumbnailSrc: string;
  thumbnailSrcDark: string;
  thumbnailAlt?: string;
  className?: string;
}

// Optimized animation variants with simpler transitions
const animationVariants = {
  "from-bottom": {
    initial: { y: "50%", opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: "50%", opacity: 0 },
  },
  "from-center": {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 },
  },
  "from-top": {
    initial: { y: "-50%", opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: "-50%", opacity: 0 },
  },
  "from-left": {
    initial: { x: "-50%", opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: "-50%", opacity: 0 },
  },
  "from-right": {
    initial: { x: "50%", opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: "50%", opacity: 0 },
  },
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  "top-in-bottom-out": {
    initial: { y: "-50%", opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: "50%", opacity: 0 },
  },
  "left-in-right-out": {
    initial: { x: "-50%", opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: "50%", opacity: 0 },
  },
};

// Create memoized components for better performance
const VideoButton = memo(({ onClick }: { onClick: () => void }) => (
  <div className="flex size-20 items-center justify-center rounded-full bg-gradient-to-b from-primary/30 to-primary shadow-md transform-gpu">
    <Play
      className="size-7 fill-white text-white"
      style={{
        filter: "drop-shadow(0 2px 2px rgb(0 0 0 / 0.1))",
      }}
    />
  </div>
));

VideoButton.displayName = "VideoButton";

function HeroVideoDialog({
  animationStyle = "from-center",
  videoSrc,
  thumbnailSrc,
  thumbnailSrcDark,
  thumbnailAlt = "Video thumbnail",
  className,
}: HeroVideoProps) {
  const [isVideoOpen, setIsVideoOpen] = useState(false);
  const selectedAnimation = animationVariants[animationStyle];

  const openVideo = useCallback(() => setIsVideoOpen(true), []);
  const closeVideo = useCallback(() => setIsVideoOpen(false), []);

  return (
    <motion.div
      className={cn("relative", className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div
        className="group relative overflow-hidden border border-primary/20 shadow shadow-primary/20 rounded-2xl cursor-pointer hover:shadow-primary/30 hover:scale-[1.02] transition-all duration-200 ease-out"
        onClick={openVideo}
      >
        <img
          src={thumbnailSrc}
          alt={thumbnailAlt}
          width={1920}
          height={1080}
          loading="lazy"
          decoding="async"
          className="w-full dark:hidden"
        />
        <img
          src={thumbnailSrcDark}
          alt={thumbnailAlt}
          width={1920}
          height={1080}
          loading="lazy"
          decoding="async"
          className="w-full hidden dark:block"
        />

        {/* Primary overlay on hover */}
        <div className="absolute inset-0 bg-primary/0 group-hover:bg-primary/5 transition-all duration-200 ease-out"></div>

        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex size-24 items-center justify-center rounded-full bg-primary/10 backdrop-blur-[2px]">
            <VideoButton onClick={openVideo} />
          </div>
        </div>
      </div>

      <AnimatePresence>
        {isVideoOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeVideo}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm transform-gpu"
            transition={{ duration: 0.2 }}
          >
            <motion.div
              {...selectedAnimation}
              transition={{ type: "tween", duration: 0.2 }}
              className="relative mx-4 aspect-video w-full max-w-4xl md:mx-0 transform-gpu"
              onClick={(e) => e.stopPropagation()}
            >
              <motion.button
                className="absolute -top-12 right-0 rounded-full bg-neutral-900/70 p-2 text-xl text-white"
                onClick={closeVideo}
              >
                <XIcon className="size-5" />
              </motion.button>

              <div className="relative z-[1] size-full overflow-hidden rounded-xl border-2 border-white/80">
                <iframe
                  src={videoSrc}
                  title="Video content"
                  className="size-full rounded-xl"
                  allowFullScreen
                  loading="lazy"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                ></iframe>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export default memo(HeroVideoDialog);
