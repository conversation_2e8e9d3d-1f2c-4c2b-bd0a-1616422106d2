"use client";

import { Ripple } from "@/components/magicui/ripple";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import React from "react";

interface CTAProps {
  title?: string;
  subtitle?: string;
  primaryButtonText?: string;
  primaryButtonUrl?: string;
}

const RippleBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute inset-0 [mask-image:radial-gradient(circle_at_center,white,transparent_80%)]">
        <Ripple
          mainCircleSize={400}
          mainCircleOpacity={0.25}
          numCircles={8}
          className="opacity-70 [--ripple-bg:hsl(var(--primary)_/_0.15)]"
          style={
            {
              "--foreground": "var(--primary)",
            } as React.CSSProperties
          }
        />
      </div>
    </div>
  );
};

export default function CTAWilliam({
  title = "Ready to grow your Linkedin brand?",
  subtitle = "Start your free trial today.",
  primaryButtonText = "Try William for Free",
  primaryButtonUrl = "https://william.tryinloop.com",
}: CTAProps) {
  return (
    <section className="py-24 pb-0 overflow-hidden relative">
      {/* Large background blur circles */}
      <div className="max-w-6xl mx-auto relative">
        <div className="relative p-12 sm:p-20 rounded-3xl shadow shadow-primary/20 overflow-hidden bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-md border border-primary/20">
          {/* Ripple effect as dynamic background */}
          <RippleBackground />

          <div className="relative z-10 mx-auto">
            <div className="mx-auto max-w-4xl text-center">
              <motion.div
                className="inline-block mb-5 px-6 py-2 rounded-full bg-primary/20 text-primary text-sm md:text-base font-medium"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                William - AI Colleague for Linkedin
              </motion.div>

              <motion.h2
                className="mb-6 text-4xl font-bold tracking-tight md:text-5xl lg:text-5xl xl:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground/95 to-foreground/80"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                {title}
              </motion.h2>

              <motion.p
                className="mx-auto mb-12 text-lg md:text-xl lg:text-2xl text-muted-foreground/90"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {subtitle}
              </motion.p>

              <motion.div
                className="flex justify-center mb-20"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Button asChild className="rounded-full cursor-pointer">
                  <Link
                    href={primaryButtonUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {primaryButtonText}
                  </Link>
                </Button>
              </motion.div>
            </div>
          </div>

          <div className="rounded-2xl relative z-10 sm:-mb-72 md:-mb-64 -mb-32">
            <div className="relative rounded-2xl overflow-hidden border border-primary/10 shadow shadow-primary/20 transition-all duration-500 hover:shadow-md">
              <Image
                src="/images/william-scratch.png"
                alt="William interface preview"
                width={1200}
                height={675}
                className="w-full h-auto dark:hidden"
                priority
              />
              <Image
                src="/images/william-scratch-dark.png"
                alt="William interface preview"
                width={1200}
                height={675}
                className="w-full h-auto hidden dark:block"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
