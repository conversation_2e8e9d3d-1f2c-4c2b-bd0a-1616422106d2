"use client";

import { Comparison } from "@/content/lib";

interface ComparisonHeroProps {
  comparison: Comparison;
}

export default function ComparisonHero({ comparison }: ComparisonHeroProps) {
  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-background via-background/95 to-background/90 py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center text-center max-w-4xl mx-auto">
          <div
            className="prose prose-lg dark:prose-invert max-w-none text-center
            prose-headings:text-center prose-headings:font-bold
            prose-h1:text-4xl prose-h1:md:text-6xl prose-h1:mb-6 prose-h1:bg-gradient-to-r prose-h1:from-primary prose-h1:to-primary/70 prose-h1:bg-clip-text prose-h1:text-transparent
            prose-h2:text-xl prose-h2:md:text-2xl prose-h2:text-muted-foreground prose-h2:mb-8
            prose-p:text-lg prose-p:text-muted-foreground prose-p:mb-12 prose-p:leading-relaxed"
            dangerouslySetInnerHTML={{ __html: comparison.content }}
          />
        </div>
      </div>
    </section>
  );
}
