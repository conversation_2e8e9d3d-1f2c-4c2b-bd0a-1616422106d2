"use client";

import { Marquee } from "@/components/magicui/marquee";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Meta from "../meta";

interface TestimonialProps {
  quote: string;
  content: string;
  author: string;
  company: string;
  avatarUrl: string;
}

const testimonialData: TestimonialProps[] = [
  {
    quote:
      "In<PERSON>'s <PERSON> is honestly like having a content teammate who just gets it.",
    content:
      "I literally clicked a few times and had a week's worth of solid posts ready to go. No overthinking, no staring at a blank doc. Just good content that actually sounds like me.",
    author: "Ashish",
    company: "Amazon",
    avatarUrl: "https://avatars.githubusercontent.com/u/298115?v=4",
  },
  {
    quote: "Our sales team finally looks like we know what we're doing online.",
    content:
      "We used to barely post. Now everyone's got smart, on-brand content going out every week. Feels like we're building real credibility with prospects before they even talk to us.",
    author: "<PERSON>",
    company: "Booking.com",
    avatarUrl: "https://avatars.githubusercontent.com/u/221544?v=4",
  },
  {
    quote:
      "I gave it a few ideas and it turned them into LinkedIn posts I'm proud to share.",
    content:
      "Writing used to take forever — now it takes 5 minutes, tops. Magic Create and the templates are a lifesaver when I'm short on time but still want to show up.",
    author: "Nigel",
    company: "Stripe",
    avatarUrl: "https://avatars.githubusercontent.com/u/128877?v=4",
  },
  {
    quote: "This made posting consistently way easier than I thought it'd be.",
    content:
      "The calendar keeps us on track, and those trending signals - wow. We've started jumping into convos early — and it's actually driving engagement.",
    author: "Corey",
    company: "Deloitte",
    avatarUrl: "https://avatars.githubusercontent.com/u/443366?v=4",
  },
];

const QuoteIcon = () => (
  <svg
    className="w-8 h-8 text-primary/20 absolute top-4 right-4"
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
  </svg>
);

const TestimonialCard = ({
  quote,
  content,
  author,
  company,
  avatarUrl,
}: TestimonialProps) => {
  return (
    <div className="flex-shrink-0 bg-card/60 w-[380px] p-6 rounded-xl border border-primary/20  bg-gradient-to-br from-primary/5 to-background/20 shadow-sm mx-4 flex flex-col justify-between h-auto relative group hover:shadow-md transition-all duration-300">
      <QuoteIcon />

      <div className="mb-6">
        <h3 className="text-xl font-semibold text-foreground leading-tight mb-3 pr-6">
          &quot;{quote}&quot;
        </h3>
        <p className="text-sm text-muted-foreground leading-relaxed">
          {content}
        </p>
      </div>

      <div className="flex items-center pt-4 border-t border-border/10">
        <Avatar className="h-10 w-10 rounded-full bg-primary/10 overflow-hidden border border-primary/20">
          <AvatarImage src={avatarUrl} alt={author} />
          <AvatarFallback>{author.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-foreground">{author}</p>
          <p className="text-xs text-muted-foreground">{company}</p>
        </div>
      </div>
    </div>
  );
};

export default function Testimonials() {
  return (
    <section className="py-20 overflow-hidden relative">
      {/* Background elements */}
      <div className="absolute top-1/4 -right-64 w-[600px] h-[600px] bg-primary/5 blur-[130px] rounded-full opacity-60" />
      <div className="absolute bottom-1/4 -left-64 w-[500px] h-[500px] bg-primary/5 blur-[110px] rounded-full opacity-60" />

      <div className="relative z-10">
        <Meta
          heading="What our customers are saying"
          subheading="See how Inloop is helping professionals and teams create better content faster."
          iconText="Trusted and used by Founders and Business Leaders"
          className="mb-12"
        />

        <div className="mb-8 relative">
          <Marquee className="py-4" pauseOnHover>
            {testimonialData.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </Marquee>
        </div>
      </div>
    </section>
  );
}
