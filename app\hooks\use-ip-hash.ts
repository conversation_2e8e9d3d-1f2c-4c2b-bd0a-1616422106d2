import { useCallback, useEffect, useState } from "react";

const IP_STORAGE_KEY = "ip_object";
const CACHE_DURATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

export function useIpHash() {
  const [ipHash, setIpHash] = useState<string | null>(null);

  // Fetch IP and create hash
  const fetchIpAndHash = useCallback(async (): Promise<string | null> => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();
      const ip = data.ip;

      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(ip);
      const hashBuffer = await crypto.subtle.digest("SHA-256", dataBuffer);

      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("");

      return hashHex;
    } catch (error) {
      console.error("Error getting IP hash:", error);
      return null;
    }
  }, []);

  // Store hash with expiration time
  const storeIpHash = useCallback((hash: string) => {
    const storageObject = {
      ip_hash: hash,
      exp: Date.now() + CACHE_DURATION_MS,
    };
    localStorage.setItem(IP_STORAGE_KEY, JSON.stringify(storageObject));
    setIpHash(hash);
  }, []);

  // Get hash from storage if valid
  const getStoredIpHash = useCallback(() => {
    const storedValue = localStorage.getItem(IP_STORAGE_KEY);
    if (!storedValue) return null;

    const ipObject = JSON.parse(storedValue);
    if (ipObject.exp > Date.now()) {
      return ipObject.ip_hash;
    }
    return null;
  }, []);

  useEffect(() => {
    const initialize = async () => {
      // Try to get stored hash first
      const storedHash = getStoredIpHash();
      if (storedHash) {
        setIpHash(storedHash);
        return;
      }

      // If no valid stored hash, fetch a new one
      const newHash = await fetchIpAndHash();
      if (newHash) {
        storeIpHash(newHash);
      }
    };

    initialize();
  }, [fetchIpAndHash, getStoredIpHash, storeIpHash]);

  return { ipHash };
}
