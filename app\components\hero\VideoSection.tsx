import { motion, useMotionTemplate } from "framer-motion";
import { RefObject } from "react";

// Video Fallback Component
const VideoFallback = () => (
  <div className="absolute inset-0 flex flex-col items-center justify-center bg-linear-to-br from-primary/10 via-secondary/5 to-accent/10 p-6">
    <div className="w-24 h-24 mb-6 rounded-full bg-linear-to-r from-primary/30 via-secondary/20 to-primary/30 flex items-center justify-center">
      <div className="text-3xl font-semibold text-primary/70">AI</div>
    </div>
    <div className="text-center">
      <h3 className="text-lg font-medium text-foreground mb-2">AI-Powered Sales Workflow</h3>
      <p className="text-sm text-muted-foreground">Transform your sales process with intelligent automation</p>
    </div>
  </div>
);

// Video Player Component
const VideoPlayer = ({ videoRef, setVideoError }: {
  videoRef: RefObject<HTMLVideoElement>;
  setVideoError: (error: boolean) => void;
}) => (
  <>
    <video
      ref={videoRef}
      autoPlay
      loop
      muted
      playsInline
      className="absolute inset-0 w-full h-full object-cover"
      poster="/hero-3.png"
      onError={() => setVideoError(true)}
    >
      <source src="/videos/hero.mp4" type="video/mp4" />
      Your browser does not support the video tag.
    </video>
    <div className="absolute inset-0 bg-linear-to-t from-background/40 via-background/10 to-transparent pointer-events-none"></div>
    <div className="absolute inset-0 border border-border/10 pointer-events-none"></div>

    <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center bg-linear-to-t from-background/40 via-background/30 to-background/20 backdrop-blur-xs">
      <button
        onClick={() => {
          if (videoRef.current) {
            videoRef.current.paused ? videoRef.current.play() : videoRef.current.pause();
          }
        }}
        className="w-16 h-16 rounded-full bg-linear-to-br from-primary/90 to-primary/70 flex items-center justify-center text-background hover:from-primary hover:to-primary/90 transition-colors duration-200"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>
      </button>
    </div>
  </>
);

// Video Decorations Component
const VideoDecorations = ({ mousePosition }: { mousePosition: { x: number; y: number } }) => (
  <>
    <motion.div
      className="absolute -top-12 -left-12 w-32 h-32 opacity-70 z-10 hidden lg:block"
      style={{
        x: useMotionTemplate`calc(${mousePosition.x}px * 0.07)`,
        y: useMotionTemplate`calc(${mousePosition.y}px * 0.07)`,
      }}
      initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
      animate={{ opacity: 0.7, scale: 1, rotate: 0 }}
      transition={{ duration: 1.2, delay: 0.8 }}
    >
      <div className="w-full h-full rounded-full bg-linear-to-br from-primary/30 via-secondary/20 to-primary/10 flex items-center justify-center blur-xs">
        <div className="w-3/4 h-3/4 rounded-full bg-linear-to-tl from-primary/40 via-secondary/30 to-primary/20 blur-[1px]"></div>
      </div>
    </motion.div>

    <motion.div
      className="absolute -bottom-12 -right-12 w-32 h-32 opacity-70 z-10 hidden lg:block"
      style={{
        x: useMotionTemplate`calc(${mousePosition.x}px * -0.07)`,
        y: useMotionTemplate`calc(${mousePosition.y}px * -0.07)`,
      }}
      initial={{ opacity: 0, scale: 0.8, rotate: 10 }}
      animate={{ opacity: 0.7, scale: 1, rotate: 0 }}
      transition={{ duration: 1.2, delay: 1.0 }}
    >
      <div className="w-full h-full rounded-full bg-linear-to-br from-secondary/30 via-accent/20 to-primary/10 flex items-center justify-center blur-xs">
        <div className="w-3/4 h-3/4 rounded-full bg-linear-to-tl from-secondary/40 via-accent/30 to-primary/20 blur-[1px]"></div>
      </div>
    </motion.div>
  </>
);

// Main Video Section Component
interface VideoSectionProps {
  videoY: any;
  mousePosition: { x: number; y: number };
  videoRef: RefObject<HTMLVideoElement>;
  videoError: boolean;
  setVideoError: (error: boolean) => void;
}

export const VideoSection = ({
  videoY,
  mousePosition,
  videoRef,
  videoError,
  setVideoError,
}: VideoSectionProps) => {
  return (
    <motion.div
      className="w-full max-w-4xl px-4 sm:px-8 z-40 relative"
      style={{ y: videoY, willChange: "transform" }}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
    >
      <div className="relative">
        <motion.div
          className="absolute -inset-2 border border-primary/10 rounded-2xl z-0 bg-linear-to-b from-primary/[0.02] to-transparent"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, duration: 0.8 }}
        />
        <motion.div
          className="absolute -inset-1 border border-primary/20 rounded-xl z-0 bg-linear-to-tr from-primary/[0.03] to-transparent"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1, duration: 0.8 }}
        />

        <motion.div
          className="relative z-10 overflow-hidden rounded-lg shadow-xl"
          style={{
            x: useMotionTemplate`calc(${mousePosition.x}px * -0.02)`,
            y: useMotionTemplate`calc(${mousePosition.y}px * -0.02)`,
            willChange: "transform",
          }}
        >
          <div className="aspect-video relative bg-linear-to-br from-secondary/10 via-primary/5 to-accent/5 overflow-hidden">
            {videoError ? (
              <VideoFallback />
            ) : (
              <VideoPlayer videoRef={videoRef} setVideoError={setVideoError} />
            )}
            <img
              src="/images/hero-3.png"
              alt="Hero Video"
              className="w-full h-full object-cover"
            />
          </div>

          <div className="absolute top-4 left-4 p-2 bg-linear-to-r from-background/95 to-background/80 border border-border/30 rounded-lg shadow-lg backdrop-blur-xs z-20">
            <div className="text-xs font-mono text-primary">AI-powered workflow</div>
          </div>

          <div className="absolute bottom-4 right-4 p-2 bg-linear-to-l from-background/95 to-background/80 border border-border/30 rounded-lg shadow-lg backdrop-blur-xs z-20">
            <div className="text-xs font-mono text-primary">Built for sales teams</div>
          </div>
        </motion.div>
      </div>

      <VideoDecorations mousePosition={mousePosition} />
    </motion.div>
  );
};

export default VideoSection; 