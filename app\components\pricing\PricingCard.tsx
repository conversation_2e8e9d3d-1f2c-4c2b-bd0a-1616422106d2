import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import Link from "next/link";
import AnimatedButton from "../common/AnimatedButton";

export interface PricingFeature {
  text: string;
}

export interface PricingTier {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  features: PricingFeature[];
  buttonText: string;
  buttonHref?: string;
  highlighted?: boolean;
}

interface PricingCardProps {
  tier: PricingTier;
  isYearly: boolean;
  className?: string;
}

const doNotDisplayYearlyPrice = ["enterprise", "free"];

export default function PricingCard({
  tier,
  isYearly,
  className,
}: PricingCardProps) {
  const price = isYearly ? tier.price.yearly : tier.price.monthly;
  const isEnterprise = tier.id === "enterprise";
  const originalYearlyPrice = tier.price.monthly * 12;

  return (
    <div
      className={cn(
        "rounded-xl border border-primary/20 p-8 flex flex-col",
        "bg-card/60 bg-gradient-to-br from-primary/5 to-background/20 shadow-sm",
        "transition-all duration-300 hover:shadow-md relative",
        "pt-12 md:pt-8",
        tier.highlighted && "ring-1 ring-primary/30",
        className
      )}
    >
      {tier.highlighted && (
        <AnimatedButton
          variant="badge"
          size="sm"
          position={{ top: "0", left: "50%" }}
          className="w-32 justify-center"
        >
          Popular
        </AnimatedButton>
      )}

      <div>
        <h3
          id={`tier-${tier.id}`}
          className="text-xl font-semibold text-foreground leading-tight mb-4"
        >
          {tier.name}
        </h3>

        <div className="text-sm text-muted-foreground line-through md:h-6">
          {isYearly && !doNotDisplayYearlyPrice.includes(tier.id)
            ? `$${originalYearlyPrice}`
            : null}
        </div>
        <div className="flex items-baseline gap-x-2 mb-4">
          {!isEnterprise ? (
            <>
              <span className="text-4xl font-semibold tracking-tight text-foreground">
                ${price}
              </span>
              <span className="text-base text-muted-foreground">
                {isYearly ? "/year" : "/month"}
              </span>
            </>
          ) : (
            <span className="text-2xl font-semibold tracking-tight text-foreground">
              Custom pricing
            </span>
          )}
        </div>

        <p className="text-sm text-muted-foreground leading-relaxed mb-6">
          {tier.description}
        </p>
      </div>

      <div className="flex flex-col justify-between h-full">
        <ul role="list" className="space-y-3 text-sm pb-8">
          {tier.features.map((feature, index) => (
            <li key={index} className="flex items-start gap-x-3">
              <Check className="h-5 w-5 flex-none text-primary mt-0.5" />
              <span
                className={
                  feature.text.includes("Access to William")
                    ? "text-orange-500 font-bold"
                    : "text-muted-foreground"
                }
              >
                {feature.text}
              </span>
            </li>
          ))}
        </ul>

        <Link
          href={tier.buttonHref ?? "https://william.tryinloop.com/login"}
          aria-describedby={`tier-${tier.id}`}
          className={cn(
            "w-full rounded-md px-4 py-3 text-center font-medium text-sm",
            "transition-all duration-200 inline-flex items-center justify-center",
            "focus-visible:outline-2 focus-visible:outline-offset-2 shadow-sm",
            tier.highlighted
              ? "bg-primary text-white hover:bg-primary/90 focus-visible:outline-primary"
              : "bg-primary/20 text-primary border border-primary/20 hover:bg-primary/5"
          )}
        >
          {tier.buttonText}
        </Link>
      </div>
    </div>
  );
}
