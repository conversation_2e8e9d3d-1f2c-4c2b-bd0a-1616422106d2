// Platform configuration - easily extensible for new platforms
export interface PlatformConfig {
  name: string;
  regex: RegExp;
  color: string;
  status: "active" | "coming-soon";
  extractVideoId: (url: string) => string;
}

export interface VideoOption {
  url: string;
  quality?: string;
  label: string;
  sizeText?: string;
  extension?: string;
}

export interface AudioOption {
  url: string;
  label: string;
  sizeText?: string;
  extension?: string;
}

export interface VideoResponse {
  title?: string;
  thumbnail?: string;
  videos: Array<{
    url: string;
    quality?: string;
    sizeText?: string;
    extension?: string;
  }>;
  audios?: Array<{
    url: string;
    sizeText?: string;
    extension?: string;
  }>;
}

export const PLATFORMS: Record<string, PlatformConfig> = {
  Linkedin: {
    name: "LinkedIn",
    regex: /https?:\/\/(www\.)?linkedin\.com\/posts\/[\w\-_%]+/,
    color: "blue",
    status: "active",
    extractVideoId: (url: string) => {
      const urlObj = new URL(url);
      const urn = urlObj.pathname.split("-");
      return urn[urn.length - 2];
    },
  },
  Youtube: {
    name: "YouTube",
    regex: /https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[^\s&?/]+/,
    color: "red",
    status: "active",
    extractVideoId: (url: string) => {
      const urlObj = new URL(url);
      if (url.includes("youtu.be/")) {
        return urlObj.pathname.slice(1);
      }
      return urlObj.searchParams.get("v") || "";
    },
  },
};

// Utility functions for platform detection and response processing
export const detectPlatform = (inputUrl: string): string | null => {
  for (const [key, config] of Object.entries(PLATFORMS)) {
    if (config.regex.test(inputUrl)) {
      return key;
    }
  }
  return null;
};

export const processVideoResponse = (
  data: VideoResponse,
  platform: string
): VideoOption[] => {
  if (!data.videos || data.videos.length === 0) {
    throw new Error("No videos found in response");
  }

  switch (platform) {
    case "Linkedin":
      return [
        {
          url: data.videos[0].url,
          label: "LinkedIn Video",
          sizeText: data.videos[0].sizeText,
          extension: data.videos[0].extension,
        },
      ];

    case "Youtube":
      return data.videos.map((video, index) => ({
        url: video.url,
        quality: video.quality,
        label: video.quality ? `${video.quality}` : `Option ${index + 1}`,
        sizeText: video.sizeText,
        extension: video.extension,
      }));

    default:
      return data.videos.map((video, index) => ({
        url: video.url,
        quality: video.quality,
        label: video.quality || `Option ${index + 1}`,
        sizeText: video.sizeText,
        extension: video.extension,
      }));
  }
};

export const processAudioResponse = (
  data: VideoResponse,
  platform: string
): AudioOption[] => {
  if (!data.audios || data.audios.length === 0) {
    return [];
  }

  switch (platform) {
    case "Youtube":
      return data.audios.map((audio, index) => ({
        url: audio.url,
        label: `Audio ${audio.extension?.toUpperCase() || "Format"} (${
          audio.sizeText || "Unknown size"
        })`,
        sizeText: audio.sizeText,
        extension: audio.extension,
      }));

    default:
      return data.audios.map((audio, index) => ({
        url: audio.url,
        label: `Audio ${index + 1}`,
        sizeText: audio.sizeText,
        extension: audio.extension,
      }));
  }
};

// Function to add new platforms dynamically
export const addPlatform = (key: string, config: PlatformConfig) => {
  PLATFORMS[key] = config;
};
