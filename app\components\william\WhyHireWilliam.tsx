"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useState } from "react";

const steps = [
  {
    img: "/images/magic-create.png",
    imgDark: "/images/magic-create-dark.png",
    title: "Magic Create",
    description:
      "Ask William to create a week's worth of personalized impactful linkedin content and schedule it",
  },
  {
    img: "/images/why-william-1.png",
    imgDark: "/images/why-william-1-dark.png",
    title: "Inspiration Hub",
    description:
      "Tap into 1000s of trending posts or kickstart your idea with proven viral templates",
  },
  {
    img: "/images/why-william-2.png",
    imgDark: "/images/hero-dark.png",
    title: "Topical Signals",
    description:
      "Stay ahead of competition with your unique take on the latest industry news and trends",
  },
  {
    img: "/images/why-william-3.png",
    imgDark: "/images/hero-dark.png",
    title: "Content Calendar",
    description:
      "Visualize your and team's calendar and stay on top of the game consistently",
  },
];

const HowItWorks = () => {
  const [active, setActive] = useState(0);

  return (
    <div className="relative max-w-5xl m-auto">
      <div className="hidden lg:block relative mt-12">
        <motion.div
          className="rounded-2xl overflow-hidden aspect-video relative border  border-primary/20 shadow shadow-primary/20"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <img
            src={steps[active].img}
            alt={steps[active].title}
            className="w-full h-full object-cover dark:hidden"
          />
          <img
            src={steps[active].imgDark}
            alt={steps[active].title}
            className="w-full h-full object-cover hidden dark:block"
          />
        </motion.div>
      </div>

      <div className="hidden lg:grid mt-8 grid-cols-2 gap-6">
        {steps.map((s, idx) => (
          <StepsCard
            key={idx}
            heading={s.title}
            description={s.description}
            active={active === idx}
            action={() => setActive(idx)}
          />
        ))}
      </div>

      <div className="grid lg:hidden mt-8 grid-cols-1 gap-8">
        {steps.map((s, idx) => (
          <motion.div
            key={idx}
            className={cn(
              "relative p-6 rounded-2xl bg-linear-to-br from-background/40 to-background/20 backdrop-blur-xs border border-primary/30",
              steps.length - 1 === idx ? "" : ""
            )}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: idx * 0.2 }}
          >
            <h3 className="text-lg sm:text-2xl xl:text-3xl font-semibold bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
              {s.title}
            </h3>
            <p className="text-base mt-2 text-muted-foreground">
              {s.description}
            </p>
            <motion.div
              className="rounded-2xl overflow-hidden aspect-video h-auto w-full mt-6"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="relative w-full h-full">
                <div className="absolute -inset-[1px] bg-linear-to-b from-primary/20 via-secondary/10 to-primary/20 rounded-2xl" />
                <div className="absolute inset-[1px] rounded-2xl overflow-hidden border border-primary/20 shadow-[0_0_15px_rgba(249,115,22,0.2)] transition-all">
                  <div className="absolute inset-0 w-full h-full pointer-events-none rounded-2xl shadow-[0_0_32px_8px_rgba(249,115,22,0.35)]" />
                  <img
                    src={s.img}
                    alt={s.title}
                    className="w-full h-full object-cover dark:hidden"
                  />
                  <img
                    src={s.imgDark}
                    alt={s.title}
                    className="w-full h-full object-cover hidden dark:block"
                  />
                  <div className="absolute inset-0 bg-linear-to-t from-background/40 via-background/20 to-transparent" />
                </div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

const StepsCard = (props: {
  heading: string;
  description: string;
  active: boolean;
  action: () => void;
}) => {
  return (
    <motion.div
      className={cn(
        "relative p-6 rounded-2xl transition-all duration-500",
        props.active
          ? "bg-linear-to-br from-primary/10 via-secondary/5 to-background/5 backdrop-blur-xs border-primary/20"
          : "hover:bg-linear-to-br hover:from-primary/5 hover:via-secondary/5 hover:to-background/5",
        "border border-primary/10 cursor-pointer group"
      )}
      onClick={props.action}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        initial={false}
        animate={{
          opacity: props.active ? 1 : 0.7,
          y: props.active ? 0 : 5,
        }}
        transition={{ duration: 0.3 }}
      >
        <h3 className="text-lg sm:text-xl xl:text-xl font-bold bg-clip-text text-transparent bg-linear-to-r from-foreground to-foreground/80">
          {props.heading}
        </h3>
        <p className="text-base mt-2 text-muted-foreground group-hover:text-muted-foreground/80 transition-colors">
          {props.description}
        </p>
      </motion.div>

      <div
        className={cn(
          "absolute bottom-0 left-0 right-0 h-[2px] bg-linear-to-r from-transparent via-primary/50 to-transparent transition-opacity duration-300",
          props.active ? "opacity-100" : "opacity-0"
        )}
      />
    </motion.div>
  );
};

export default HowItWorks;
