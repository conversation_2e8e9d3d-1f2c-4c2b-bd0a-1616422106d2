import { Metadata } from "next";
import Hero from "../components/hero";
import Testimonials from "../components/william/Testimonials";
import FAQSection from "../components/FAQSection";
import CTAWilliam from "../components/william/CTAWilliam";
import SolutionWilliam from "../components/william/SolutionWilliam";

export const metadata: Metadata = {
  title: "Create Viral LinkedIn Content",
  description:
    "<PERSON>, your AI Colleague, turns you and your Team into a fleet of LinkedIn Micro Influencers without lifting a finger. Take inspiration from 1000s of premium trending posts, viral templates, and industry news.",
  keywords:
    "tryinloop, william ai agent, william ai colleague, sales team micro influencers, ai for sales teams",
  openGraph: {
    title: "Create Viral LinkedIn Content",
    description:
      "<PERSON>, your AI Colleague, turns you and your Team into a fleet of LinkedIn Micro Influencers without lifting a finger. Take inspiration from 1000s of premium trending posts, viral templates, and industry news.",
    url: "https://tryinloop.com/linkedin",
    images: [
      {
        url: "https://tryinloop.com/meta-images/linkedin.png",
        width: 800,
        height: 400,
      },
    ],
  },
};

export default function LinkedInPage() {
  const heroHeadingLine1 = "Create Viral -";
  const heroHeadingLine2 = "LinkedIn Content";
  const heroParagraph =
    "William, your AI Colleague, turns you and your Team into a fleet of LinkedIn Micro Influencers without lifting a finger. Take inspiration from 1000s of premium trending posts, viral templates, and industry news.";

  const trustedBySectionHeading =
    "Trusted by more than 100s of professionals, leaders and founders for Linkedin";
  const solutionWilliamHeading =
    "Thought Leadership for Ambitious Professionals";
  const whyWilliamSubHeading =
    "William, your AI Colleague, integrates with your marketing stack, automates thought leadership, builds trust and accelerates your Linkedin Influence.";

  return (
    <main className="relative mt-16">
      <Hero
        headingLine1={heroHeadingLine1}
        headingLine2={heroHeadingLine2}
        paragraph={heroParagraph}
        trustedBySectionHeading={trustedBySectionHeading}
      />
      <SolutionWilliam
        heading={solutionWilliamHeading}
        whyWilliamSubHeading={whyWilliamSubHeading}
      />
      <Testimonials />
      <FAQSection />
      <CTAWilliam title="Ready to grow your Linkedin Influence?" />
    </main>
  );
}
