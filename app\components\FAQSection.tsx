"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import Meta from "./meta";

type FAQItem = {
  question: string;
  answer: React.ReactNode;
};

type FAQSectionProps = {
  heading?: string;
  subheading?: string;
  iconText?: string;
  faqItems?: FAQItem[];
  className?: string;
};

export default function FAQSection({
  heading = "Frequently Asked Questions",
  subheading = "Everything you need to know about <PERSON> and <PERSON><PERSON>",
  iconText = "Your Questions Answered",
  faqItems,
  className,
}: FAQSectionProps) {
  const defaultFaqItems: FAQItem[] = [
    {
      question: "How can <PERSON> help me grow my audience?",
      answer:
        "<PERSON>, your personal AI Agent, helps you stay consistently visible with engaging, high-performing posts. He taps into trending topics, analyzes what's working in your industry, and creates personalized LinkedIn content that positions you as a thought leader—so your audience grows, your credibility rises, and your momentum accelerates.",
    },
    {
      question: "What can I try during <PERSON><PERSON>'s free trial?",
      answer:
        "The free trial gives you 4 content credits to test <PERSON><PERSON>'s AI-powered post creation, explore the Inspiration Hub, and preview your personal content calendar. It's a risk-free way to experience how William can transform your content game—no credit card required.",
    },
    {
      question: "Can I cancel anytime? How?",
      answer:
        "Absolutely. You can cancel anytime directly from your account settings under the “Billing” tab. No hoops, no hard feelings. Your subscription will remain active until the end of the current billing cycle.",
    },
    {
      question: "Is William an AI Agent?",
      answer:
        " Yes, William is an AI Agent specializing in LinkedIn content creation. He’s trained on thousands of viral posts and uses a library of trending content to build your posts.",
    },
    {
      question: "Can William fully automate my LinkedIn content pipeline?",
      answer:
        "Yes, William can fully automate your LinkedIn content pipeline. With Magic Create, he researches your industry for the latest news, finds posts that amplify your voice, and builds a week’s worth of content. He then schedules the posts on LinkedIn, all visible in your content calendar.",
    },
    {
      question: "Can I schedule posts on LinkedIn?",
      answer:
        "Yes, you can schedule posts on LinkedIn using Inloop. Every post will be visible in your content calendar.",
    },
    {
      question: "What is the pricing for William?",
      answer:
        "You can hire William, your AI Agent for LinkedIn content automation, for as low as $10 per month.",
    },
    {
      question: "How is William different from ChatGPT?",
      answer:
        "Unlike ChatGPT, which is general-purpose, William automates content strategy, tracks trends, and manages team calendars—making it a hands-on AI colleague built specifically for Linkedin.",
    },
  ];

  const items = faqItems || defaultFaqItems;

  return (
    <section className={cn("py-16 w-full max-w-6xl mx-auto", className)}>
      <Meta
        heading={heading}
        subheading={subheading}
        iconText={iconText}
        className="mb-12"
      />

      <div className="space-y-4">
        {items.map((item, index) => (
          <FAQItem key={index} question={item.question} answer={item.answer} />
        ))}
      </div>
    </section>
  );
}

function FAQItem({ question, answer }: FAQItem) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-card/60 border border-primary/20 bg-gradient-to-br from-primary/5 to-background/20 shadow-sm backdrop-blur-xs rounded-xl border border-border/5 overflow-hidden">
      <button
        className="w-full px-6 py-5 flex items-center justify-between text-left focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-lg md:text-xl font-medium text-foreground">
          {question}
        </h3>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1.0] }}
        >
          <ChevronDown className="w-5 h-5 text-primary" />
        </motion.div>
      </button>

      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            key="content"
            initial="collapsed"
            animate="open"
            exit="collapsed"
            variants={{
              open: { opacity: 1, height: "auto", marginTop: "0.5rem" },
              collapsed: { opacity: 0, height: 0, marginTop: 0 },
            }}
            transition={{
              duration: 0.5,
              ease: [0.25, 0.1, 0.25, 1.0],
            }}
            className="px-6 overflow-hidden"
          >
            <motion.div
              variants={{
                collapsed: { opacity: 0, y: -10 },
                open: { opacity: 1, y: 0 },
              }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="pb-6 text-muted-foreground/90 leading-relaxed prose-sm max-w-none dark:prose-invert"
            >
              {typeof answer === "string" ? <p>{answer}</p> : answer}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
