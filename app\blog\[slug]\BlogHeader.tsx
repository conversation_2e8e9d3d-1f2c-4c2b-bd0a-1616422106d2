import Image from "next/image";
import { Blog } from "@/content/lib";

const BlogHeader = ({ blog }: { blog: Blog }) => {
  return (
    <div className="space-y-6">
      <h1 className="mb-1 text-4xl font-bold text-foreground">{blog.title}</h1>
      <p className="mb-2 text-muted-foreground text-lg leading-relaxed">
        {blog.description}
      </p>
      <div className="relative w-full mx-auto aspect-video">
        <Image
          priority
          fill={true}
          src={blog.coverImage}
          className="object-cover rounded-lg"
          alt={blog.title}
        />
      </div>
    </div>
  );
};

export default BlogHeader;
