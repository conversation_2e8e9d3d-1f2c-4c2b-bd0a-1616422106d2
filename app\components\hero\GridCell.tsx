import { motion } from "framer-motion";

interface GridCellProps {
  index: number;
  className?: string;
}

export const GridCell = ({ index, className = "" }: GridCellProps) => {
  const isPrimary = index % 13 === 0;
  const isSecondary = index % 29 === 0;

  return (
    <div className={`relative ${className}`}>
      <div
        className={`absolute inset-0 border border-border/10 transform-gpu
          ${isPrimary ? "bg-linear-to-br from-primary/[0.1] to-primary/[0.01]" : ""} 
          ${isSecondary ? "bg-linear-to-tr from-secondary/[0.02] to-secondary/[0.01]" : ""}`}
      />
    </div>
  );
};

export default GridCell; 