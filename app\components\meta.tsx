import { motion } from "framer-motion";
import { Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";
export default function Meta({
  heading,
  subheading,
  iconText,
  className,
}: {
  heading: string;
  subheading: string;
  iconText: string;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "flex flex-col items-start sm:items-center gap-3 sm:text-center",
        className
      )}
    >
      <motion.div
        className="inline-flex items-center rounded-full border border-primary/20 bg-primary/5 px-4 py-1.5 text-sm font-medium text-primary backdrop-blur-sm"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
      >
        <Sparkles className="mr-1.5 h-3.5 w-3.5" />
        {iconText}
      </motion.div>

      <motion.div
        className="relative inline-block"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-4xl sm:text-5xl md:text-5xl leading-tight font-bold relative">
          <span className="relative z-10 text-foreground">{heading}</span>
          <span className="absolute left-0 top-0 bottom-0 w-[25%] bg-gradient-to-r from-primary/15 to-transparent blur-[12px] rounded-l-full -z-10"></span>
          <span className="absolute right-0 top-0 bottom-0 w-[25%] bg-gradient-to-l from-primary/15 to-transparent blur-[12px] rounded-r-full -z-10"></span>
        </h2>
      </motion.div>

      <motion.p
        className="md:text-center text-base sm:text-lg text-muted-foreground sm:leading-relaxed md:text-xl md:leading-relaxed sm:max-w-3xl"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {subheading}
      </motion.p>
    </div>
  );
}
