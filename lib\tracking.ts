import { useIpHash } from "@/app/hooks/use-ip-hash";
import { sendGTMEvent } from "@next/third-parties/google";
import { useCallback, useEffect, useState } from "react";

type TrackEventParams = {
  event: string;
  [key: string]: string | number | boolean;
};

export function useGtmTrack() {
  const { ipHash } = useIpHash();

  const trackEvent = useCallback(
    ({ event, ...rest }: TrackEventParams) => {
      sendGTMEvent({
        event,
        ...(ipHash && { ip_hash: ipHash }),
        ...rest,
      });
    },
    [ipHash]
  );

  return {
    loginTrack: () => {
      trackEvent({
        event: "login",
      });
    },
    bookDemoTrack: () => {
      trackEvent({
        event: "book_a_demo",
      });
    },
    getStartedTrack: () => {
      trackEvent({
        event: "get_started",
      });
    },
    getStartedFreeContentTrack: () => {
      trackEvent({
        event: "get_started_1_week_content_free",
      });
    },
    downloadVideoTrack: (platform: string) => {
      trackEvent({
        event: "download_video",
        platform,
      });
    },
  };
}
